{"name": "guesthouse", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@supabase/supabase-js": "^2.49.9", "date-fns": "^4.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.6.1", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/eslint-parser": "^7.27.1", "@babel/plugin-syntax-jsx": "^7.27.1", "@babel/preset-react": "^7.27.1", "@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.0.0", "stylelint": "^13.13.1", "stylelint-config-standard": "^21.0.0", "stylelint-csstree-validator": "^1.9.0", "stylelint-scss": "^3.21.0", "vite": "^6.3.5"}}