# 🏨 Ghana Guesthouse Management System

A comprehensive guesthouse management system built for Ghana, featuring online booking, admin panel, and Ghana Cedis currency support.

## 🌟 Features

### Guest Features
- **Room Browsing**: View available rooms with images and amenities
- **Online Booking**: Easy booking system with date selection
- **Payment Integration**: Support for Ghana Mobile Money and cards
- **User Dashboard**: Manage bookings and profile
- **Responsive Design**: Works on all devices

### Admin Features
- **Dashboard**: Overview of bookings, revenue, and occupancy
- **Room Management**: Add, edit, and manage room availability
- **Booking Management**: View and manage all bookings
- **Guest Management**: Customer database and communication
- **Financial Reports**: Revenue tracking and analytics
- **Payment Tracking**: Monitor all transactions

### Technical Features
- **Ghana-Specific**: Currency in Ghana Cedis (GHS), local payment methods
- **Real-time Updates**: Live booking status and availability
- **Secure Authentication**: Role-based access control
- **Mobile-First**: Optimized for mobile devices
- **Offline Support**: Basic functionality without internet

## 🛠️ Tech Stack

- **Frontend**: React 19 + Vite
- **Backend**: Supabase (PostgreSQL + Auth + Storage)
- **Styling**: Vanilla CSS with custom design system
- **State Management**: React Context + useReducer
- **Forms**: React Hook Form + Yup validation
- **Notifications**: React Hot Toast
- **Date Handling**: date-fns
- **Payment**: Paystack (recommended for Ghana)

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- Supabase account
- Paystack account (for payments)

### 1. Clone and Install
```bash
git clone <repository-url>
cd GuestHouse
npm install
```

### 2. Environment Setup
```bash
cp .env.example .env
```

Edit `.env` with your credentials:
```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_PAYSTACK_PUBLIC_KEY=your_paystack_public_key
```

### 3. Database Setup
1. Create a new Supabase project
2. Run the SQL in `database/schema.sql` in your Supabase SQL Editor
3. Run the SQL in `database/sample_data.sql` for sample data
4. Set up Row Level Security policies (included in schema)

### 4. Create Admin User
1. Sign up through the app or Supabase Auth
2. Update the user's role in the database:
```sql
UPDATE profiles SET role = 'admin' WHERE id = 'your_user_id';
```

### 5. Run Development Server
```bash
npm run dev
```

Visit `http://localhost:5173` to see the application.

## 📁 Project Structure

```
src/
├── components/
│   ├── common/           # Reusable components
│   ├── guest/           # Guest-facing components
│   ├── admin/           # Admin panel components
│   ├── auth/            # Authentication components
│   └── layout/          # Layout components
├── pages/               # Page components
├── hooks/               # Custom React hooks
├── services/            # API services (Supabase)
├── utils/               # Utility functions
├── context/             # React context providers
├── constants/           # App constants
└── styles/              # Global styles
```

## 🏗️ Development Roadmap

### Phase 1: Foundation ✅
- [x] Project setup and structure
- [x] Database schema design
- [x] Authentication system
- [x] Basic UI components
- [x] Currency utilities (Ghana Cedis)

### Phase 2: Guest Features (In Progress)
- [ ] Room listing and search
- [ ] Booking system
- [ ] Payment integration (Paystack)
- [ ] User dashboard
- [ ] Email/SMS notifications

### Phase 3: Admin Panel
- [ ] Admin dashboard
- [ ] Room management
- [ ] Booking management
- [ ] Guest management
- [ ] Financial reports

### Phase 4: Advanced Features
- [ ] Reviews and ratings
- [ ] Advanced analytics
- [ ] Mobile app (React Native)
- [ ] Multi-language support

## 💳 Payment Integration

### Paystack Setup (Recommended for Ghana)
1. Create a Paystack account
2. Get your public key from the dashboard
3. Add to environment variables
4. Supports:
   - Mobile Money (MTN, Vodafone, AirtelTigo)
   - Visa/Mastercard
   - Bank transfers

### Alternative: Flutterwave
- Also supports Ghana Mobile Money
- Good documentation and React SDK

## 🔧 Configuration

### Room Configuration
- Total rooms: 10 (configurable in constants)
- Room types: Single, Double, Suite
- Capacity: 1-4 guests per room
- Amenities: AC, WiFi, TV, etc.

### Currency Settings
- Primary currency: Ghana Cedis (GHS)
- Symbol: ₵
- Formatting: Intl.NumberFormat with Ghana locale

### Date Settings
- Format: DD/MM/YYYY (Ghana standard)
- Timezone: GMT (Ghana timezone)
- Minimum stay: 1 night
- Maximum stay: 30 nights

## 🧪 Testing

### Demo Credentials
- **Admin**: <EMAIL> / admin123
- **Guest**: <EMAIL> / guest123

### Test Payment
Use Paystack test cards for payment testing:
- **Test Card**: ****************
- **CVV**: 408
- **Expiry**: Any future date

## 🚀 Deployment

### Frontend (Vercel - Recommended)
```bash
npm run build
# Deploy to Vercel
```

### Backend
- Supabase handles hosting automatically
- Configure custom domain if needed

### Environment Variables
Ensure all production environment variables are set:
- Supabase production URLs
- Paystack live keys
- Custom domain settings

## 📞 Support

For support and questions:
- Create an issue in the repository
- Email: <EMAIL>
- Phone: +233 XX XXX XXXX

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

---

Built with ❤️ for Ghana's hospitality industry
