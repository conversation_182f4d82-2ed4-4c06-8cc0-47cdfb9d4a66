# Supabase Database Setup Instructions

## Step 1: Access Supabase SQL Editor

1. Go to your Supabase project dashboard: https://supabase.com/dashboard
2. Navigate to the "SQL Editor" tab
3. Create a new query

## Step 2: Run the Database Schema

Copy and paste the contents of `schema.sql` into the SQL editor and run it. This will create:

- **profiles** table (user profiles with roles)
- **rooms** table (room information and pricing)
- **bookings** table (reservation management)
- **payments** table (payment tracking)
- **reviews** table (guest reviews and ratings)
- Row Level Security (RLS) policies
- Database functions and triggers

## Step 3: Add Sample Data (Optional)

After running the schema, you can run `sample-data.sql` to add sample rooms and test data.

## Step 4: Verify Setup

1. Check that all tables are created in the "Table Editor"
2. Verify RLS policies are enabled
3. Test the connection by refreshing your application

## Step 5: Create Admin User

To create an admin user:

1. Register a new account through your application
2. Go to Supabase Dashboard → Authentication → Users
3. Find your user and click "Edit"
4. In the "Raw User Meta Data" section, add:
   ```json
   {
     "role": "admin",
     "full_name": "Your Name"
   }
   ```
5. Save the changes

## Troubleshooting

### Common Issues:

1. **RLS Policies**: Make sure RLS is enabled on all tables
2. **User Roles**: Ensure admin users have the correct role in their metadata
3. **Environment Variables**: Verify your `.env` file has the correct Supabase URL and key

### Testing the Connection:

1. Open browser console (F12)
2. Look for "Supabase client created successfully" message
3. Check for any error messages

### Database Access:

- Guests can only see their own bookings and reviews
- Admins can see and manage all data
- Public users can view rooms and reviews

## Next Steps

Once the database is set up:

1. Test user registration and login
2. Create some sample rooms (as admin)
3. Test the booking flow
4. Test the review system
5. Explore the admin analytics

## Support

If you encounter issues:

1. Check the browser console for errors
2. Verify your Supabase project settings
3. Ensure your API keys are correct
4. Check that RLS policies are properly configured
