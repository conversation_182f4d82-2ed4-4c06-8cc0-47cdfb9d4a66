-- Sample data for Ghana Guesthouse Management System
-- Run this after creating the schema and setting up authentication

-- Insert sample rooms (10 rooms as specified)
INSERT INTO rooms (room_number, room_type, price_per_night, capacity, amenities, description, images) VALUES
('R001', 'single', 150.00, 1, '["AC", "WiFi", "TV", "Private Bathroom"]', 'Cozy single room with modern amenities', '["room1_1.jpg", "room1_2.jpg"]'),
('R002', 'single', 150.00, 1, '["AC", "WiFi", "TV", "Private Bathroom"]', 'Comfortable single room perfect for solo travelers', '["room2_1.jpg", "room2_2.jpg"]'),
('R003', 'double', 250.00, 2, '["AC", "WiFi", "TV", "Private Bathroom", "Fridge"]', 'Spacious double room with queen bed', '["room3_1.jpg", "room3_2.jpg"]'),
('R004', 'double', 250.00, 2, '["AC", "WiFi", "TV", "Private Bathroom", "Fridge"]', 'Modern double room with city view', '["room4_1.jpg", "room4_2.jpg"]'),
('R005', 'double', 280.00, 2, '["AC", "WiFi", "TV", "Private Bathroom", "Fridge", "Balcony"]', 'Double room with private balcony', '["room5_1.jpg", "room5_2.jpg"]'),
('R006', 'suite', 450.00, 3, '["AC", "WiFi", "TV", "Private Bathroom", "Fridge", "Balcony", "Living Area"]', 'Luxury suite with separate living area', '["room6_1.jpg", "room6_2.jpg", "room6_3.jpg"]'),
('R007', 'suite', 450.00, 3, '["AC", "WiFi", "TV", "Private Bathroom", "Fridge", "Balcony", "Living Area"]', 'Executive suite with premium amenities', '["room7_1.jpg", "room7_2.jpg", "room7_3.jpg"]'),
('R008', 'double', 270.00, 2, '["AC", "WiFi", "TV", "Private Bathroom", "Fridge"]', 'Deluxe double room with modern decor', '["room8_1.jpg", "room8_2.jpg"]'),
('R009', 'single', 180.00, 1, '["AC", "WiFi", "TV", "Private Bathroom", "Work Desk"]', 'Business single room with work space', '["room9_1.jpg", "room9_2.jpg"]'),
('R010', 'suite', 500.00, 4, '["AC", "WiFi", "TV", "Private Bathroom", "Fridge", "Balcony", "Living Area", "Kitchenette"]', 'Presidential suite with kitchenette', '["room10_1.jpg", "room10_2.jpg", "room10_3.jpg", "room10_4.jpg"]);

-- Note: You'll need to create an admin user first through Supabase Auth
-- Then update the profile to set role as 'admin'
-- Example: After creating admin user <NAME_EMAIL>
-- UPDATE profiles SET role = 'admin', full_name = 'Admin User', phone = '+************' 
-- WHERE id = 'admin_user_uuid_here';

-- Sample booking data (you can add this after creating guest users)
-- INSERT INTO bookings (guest_id, room_id, check_in_date, check_out_date, total_amount, guest_count, special_requests) VALUES
-- ('guest_user_uuid', 'room_uuid', '2024-02-01', '2024-02-03', 300.00, 1, 'Late check-in requested');

-- Sample payment data (linked to bookings)
-- INSERT INTO payments (booking_id, amount, payment_method, payment_reference, status, processed_at) VALUES
-- ('booking_uuid', 300.00, 'mobile_money', 'MTN_REF_123456', 'completed', NOW());

-- Create a function to check room availability
CREATE OR REPLACE FUNCTION check_room_availability(
  room_uuid UUID,
  check_in DATE,
  check_out DATE
)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN NOT EXISTS (
    SELECT 1 FROM bookings 
    WHERE room_id = room_uuid 
    AND booking_status IN ('confirmed', 'pending')
    AND (
      (check_in_date <= check_in AND check_out_date > check_in) OR
      (check_in_date < check_out AND check_out_date >= check_out) OR
      (check_in_date >= check_in AND check_out_date <= check_out)
    )
  );
END;
$$ LANGUAGE plpgsql;

-- Create a function to get available rooms for date range
CREATE OR REPLACE FUNCTION get_available_rooms(
  check_in DATE,
  check_out DATE
)
RETURNS TABLE (
  id UUID,
  room_number TEXT,
  room_type TEXT,
  price_per_night DECIMAL,
  capacity INTEGER,
  amenities JSONB,
  images TEXT[],
  description TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    r.id,
    r.room_number,
    r.room_type,
    r.price_per_night,
    r.capacity,
    r.amenities,
    r.images,
    r.description
  FROM rooms r
  WHERE r.is_available = true
  AND check_room_availability(r.id, check_in, check_out);
END;
$$ LANGUAGE plpgsql;

-- Create a function to calculate booking statistics
CREATE OR REPLACE FUNCTION get_booking_stats(
  start_date DATE DEFAULT NULL,
  end_date DATE DEFAULT NULL
)
RETURNS TABLE (
  total_bookings BIGINT,
  total_revenue DECIMAL,
  occupancy_rate DECIMAL,
  avg_booking_value DECIMAL
) AS $$
DECLARE
  total_room_nights BIGINT;
  occupied_nights BIGINT;
BEGIN
  -- Set default dates if not provided
  IF start_date IS NULL THEN
    start_date := CURRENT_DATE - INTERVAL '30 days';
  END IF;
  
  IF end_date IS NULL THEN
    end_date := CURRENT_DATE;
  END IF;
  
  -- Calculate total possible room nights
  total_room_nights := (SELECT COUNT(*) FROM rooms WHERE is_available = true) * 
                      (end_date - start_date + 1);
  
  -- Calculate occupied nights
  SELECT 
    COUNT(*),
    SUM(total_amount),
    SUM(check_out_date - check_in_date),
    AVG(total_amount)
  INTO 
    total_bookings,
    total_revenue,
    occupied_nights,
    avg_booking_value
  FROM bookings
  WHERE booking_status IN ('confirmed', 'completed')
  AND check_in_date >= start_date
  AND check_out_date <= end_date;
  
  -- Calculate occupancy rate
  IF total_room_nights > 0 THEN
    occupancy_rate := (occupied_nights::DECIMAL / total_room_nights::DECIMAL) * 100;
  ELSE
    occupancy_rate := 0;
  END IF;
  
  -- Handle NULL values
  total_bookings := COALESCE(total_bookings, 0);
  total_revenue := COALESCE(total_revenue, 0);
  avg_booking_value := COALESCE(avg_booking_value, 0);
  
  RETURN QUERY SELECT 
    total_bookings,
    total_revenue,
    occupancy_rate,
    avg_booking_value;
END;
$$ LANGUAGE plpgsql;
