// Analytics service for advanced reporting and insights

import { supabase, TABLES } from './supabase';
import { formatDateForAPI } from '../utils/date';

export const analyticsService = {
  // Get revenue analytics
  getRevenueAnalytics: async (startDate, endDate) => {
    try {
      const { data, error } = await supabase
        .from(TABLES.BOOKINGS)
        .select(`
          total_amount,
          payment_status,
          booking_status,
          check_in_date,
          check_out_date,
          created_at
        `)
        .gte('check_in_date', formatDateForAPI(startDate))
        .lte('check_out_date', formatDateForAPI(endDate));

      if (error) throw error;

      // Calculate analytics
      const totalRevenue = data
        .filter(b => b.payment_status === 'paid')
        .reduce((sum, b) => sum + parseFloat(b.total_amount), 0);

      const pendingRevenue = data
        .filter(b => b.payment_status === 'pending' && b.booking_status === 'confirmed')
        .reduce((sum, b) => sum + parseFloat(b.total_amount), 0);

      const totalBookings = data.length;
      const confirmedBookings = data.filter(b => b.booking_status === 'confirmed').length;
      const cancelledBookings = data.filter(b => b.booking_status === 'cancelled').length;
      const completedBookings = data.filter(b => b.booking_status === 'completed').length;

      // Monthly breakdown
      const monthlyData = {};
      data.forEach(booking => {
        const month = new Date(booking.check_in_date).toISOString().slice(0, 7); // YYYY-MM
        if (!monthlyData[month]) {
          monthlyData[month] = { revenue: 0, bookings: 0 };
        }
        if (booking.payment_status === 'paid') {
          monthlyData[month].revenue += parseFloat(booking.total_amount);
        }
        monthlyData[month].bookings += 1;
      });

      return {
        data: {
          totalRevenue,
          pendingRevenue,
          totalBookings,
          confirmedBookings,
          cancelledBookings,
          completedBookings,
          averageBookingValue: totalBookings > 0 ? totalRevenue / totalBookings : 0,
          conversionRate: totalBookings > 0 ? (confirmedBookings / totalBookings) * 100 : 0,
          monthlyData: Object.entries(monthlyData).map(([month, data]) => ({
            month,
            ...data
          })).sort((a, b) => a.month.localeCompare(b.month))
        },
        error: null
      };
    } catch (error) {
      console.error('Error fetching revenue analytics:', error);
      return { data: null, error };
    }
  },

  // Get occupancy analytics
  getOccupancyAnalytics: async (startDate, endDate) => {
    try {
      // Get all rooms
      const { data: rooms, error: roomsError } = await supabase
        .from(TABLES.ROOMS)
        .select('id, room_number, room_type');

      if (roomsError) throw roomsError;

      // Get bookings in date range
      const { data: bookings, error: bookingsError } = await supabase
        .from(TABLES.BOOKINGS)
        .select(`
          room_id,
          check_in_date,
          check_out_date,
          booking_status,
          room:rooms(room_number, room_type)
        `)
        .gte('check_in_date', formatDateForAPI(startDate))
        .lte('check_out_date', formatDateForAPI(endDate))
        .in('booking_status', ['confirmed', 'completed']);

      if (bookingsError) throw bookingsError;

      // Calculate occupancy by room type
      const roomTypeOccupancy = {};
      const roomOccupancy = {};

      rooms.forEach(room => {
        if (!roomTypeOccupancy[room.room_type]) {
          roomTypeOccupancy[room.room_type] = { totalRooms: 0, occupiedNights: 0 };
        }
        roomTypeOccupancy[room.room_type].totalRooms += 1;
        roomOccupancy[room.id] = { 
          roomNumber: room.room_number, 
          roomType: room.room_type, 
          occupiedNights: 0 
        };
      });

      // Calculate occupied nights
      const start = new Date(startDate);
      const end = new Date(endDate);
      const totalDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24));

      bookings.forEach(booking => {
        const checkIn = new Date(booking.check_in_date);
        const checkOut = new Date(booking.check_out_date);
        const nights = Math.ceil((checkOut - checkIn) / (1000 * 60 * 60 * 24));

        const roomType = booking.room.room_type;
        roomTypeOccupancy[roomType].occupiedNights += nights;
        roomOccupancy[booking.room_id].occupiedNights += nights;
      });

      // Calculate percentages
      Object.keys(roomTypeOccupancy).forEach(type => {
        const data = roomTypeOccupancy[type];
        data.occupancyRate = data.totalRooms > 0 
          ? (data.occupiedNights / (data.totalRooms * totalDays)) * 100 
          : 0;
      });

      Object.keys(roomOccupancy).forEach(roomId => {
        const data = roomOccupancy[roomId];
        data.occupancyRate = (data.occupiedNights / totalDays) * 100;
      });

      return {
        data: {
          totalRooms: rooms.length,
          totalDays,
          roomTypeOccupancy,
          roomOccupancy: Object.entries(roomOccupancy).map(([id, data]) => ({
            roomId: id,
            ...data
          })),
          averageOccupancyRate: Object.values(roomTypeOccupancy)
            .reduce((sum, data) => sum + data.occupancyRate, 0) / Object.keys(roomTypeOccupancy).length
        },
        error: null
      };
    } catch (error) {
      console.error('Error fetching occupancy analytics:', error);
      return { data: null, error };
    }
  },

  // Get guest analytics
  getGuestAnalytics: async (startDate, endDate) => {
    try {
      const { data, error } = await supabase
        .from(TABLES.BOOKINGS)
        .select(`
          guest_id,
          guest_count,
          total_amount,
          booking_status,
          check_in_date,
          guest:profiles(full_name, email, created_at)
        `)
        .gte('check_in_date', formatDateForAPI(startDate))
        .lte('check_out_date', formatDateForAPI(endDate));

      if (error) throw error;

      // Guest statistics
      const uniqueGuests = new Set(data.map(b => b.guest_id)).size;
      const totalGuestNights = data
        .filter(b => b.booking_status !== 'cancelled')
        .reduce((sum, b) => sum + b.guest_count, 0);

      const repeatGuests = {};
      data.forEach(booking => {
        if (!repeatGuests[booking.guest_id]) {
          repeatGuests[booking.guest_id] = 0;
        }
        repeatGuests[booking.guest_id] += 1;
      });

      const repeatGuestCount = Object.values(repeatGuests).filter(count => count > 1).length;
      const repeatGuestRate = uniqueGuests > 0 ? (repeatGuestCount / uniqueGuests) * 100 : 0;

      // Top guests by spending
      const guestSpending = {};
      data.forEach(booking => {
        if (booking.booking_status !== 'cancelled') {
          if (!guestSpending[booking.guest_id]) {
            guestSpending[booking.guest_id] = {
              totalSpent: 0,
              bookingCount: 0,
              guestInfo: booking.guest
            };
          }
          guestSpending[booking.guest_id].totalSpent += parseFloat(booking.total_amount);
          guestSpending[booking.guest_id].bookingCount += 1;
        }
      });

      const topGuests = Object.entries(guestSpending)
        .map(([id, data]) => ({ guestId: id, ...data }))
        .sort((a, b) => b.totalSpent - a.totalSpent)
        .slice(0, 10);

      return {
        data: {
          uniqueGuests,
          totalGuestNights,
          averageGuestsPerBooking: data.length > 0 
            ? totalGuestNights / data.filter(b => b.booking_status !== 'cancelled').length 
            : 0,
          repeatGuestCount,
          repeatGuestRate,
          topGuests
        },
        error: null
      };
    } catch (error) {
      console.error('Error fetching guest analytics:', error);
      return { data: null, error };
    }
  },

  // Get performance trends
  getPerformanceTrends: async (months = 12) => {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(endDate.getMonth() - months);

      const { data, error } = await supabase
        .from(TABLES.BOOKINGS)
        .select(`
          total_amount,
          payment_status,
          booking_status,
          check_in_date,
          created_at
        `)
        .gte('created_at', formatDateForAPI(startDate));

      if (error) throw error;

      // Group by month
      const monthlyTrends = {};
      
      for (let i = 0; i < months; i++) {
        const date = new Date();
        date.setMonth(endDate.getMonth() - i);
        const monthKey = date.toISOString().slice(0, 7);
        monthlyTrends[monthKey] = {
          month: monthKey,
          revenue: 0,
          bookings: 0,
          confirmedBookings: 0,
          cancelledBookings: 0
        };
      }

      data.forEach(booking => {
        const month = new Date(booking.created_at).toISOString().slice(0, 7);
        if (monthlyTrends[month]) {
          monthlyTrends[month].bookings += 1;
          
          if (booking.payment_status === 'paid') {
            monthlyTrends[month].revenue += parseFloat(booking.total_amount);
          }
          
          if (booking.booking_status === 'confirmed') {
            monthlyTrends[month].confirmedBookings += 1;
          } else if (booking.booking_status === 'cancelled') {
            monthlyTrends[month].cancelledBookings += 1;
          }
        }
      });

      const trends = Object.values(monthlyTrends)
        .sort((a, b) => a.month.localeCompare(b.month));

      return { data: trends, error: null };
    } catch (error) {
      console.error('Error fetching performance trends:', error);
      return { data: null, error };
    }
  },

  // Get room performance analytics
  getRoomPerformance: async (startDate, endDate) => {
    try {
      const { data, error } = await supabase
        .from(TABLES.BOOKINGS)
        .select(`
          room_id,
          total_amount,
          booking_status,
          check_in_date,
          check_out_date,
          room:rooms(room_number, room_type, price_per_night)
        `)
        .gte('check_in_date', formatDateForAPI(startDate))
        .lte('check_out_date', formatDateForAPI(endDate));

      if (error) throw error;

      // Group by room
      const roomPerformance = {};
      
      data.forEach(booking => {
        const roomId = booking.room_id;
        if (!roomPerformance[roomId]) {
          roomPerformance[roomId] = {
            roomInfo: booking.room,
            totalBookings: 0,
            totalRevenue: 0,
            confirmedBookings: 0,
            cancelledBookings: 0,
            averageStayLength: 0,
            totalNights: 0
          };
        }

        const perf = roomPerformance[roomId];
        perf.totalBookings += 1;

        if (booking.booking_status !== 'cancelled') {
          perf.totalRevenue += parseFloat(booking.total_amount);
          const nights = Math.ceil(
            (new Date(booking.check_out_date) - new Date(booking.check_in_date)) / (1000 * 60 * 60 * 24)
          );
          perf.totalNights += nights;
        }

        if (booking.booking_status === 'confirmed') {
          perf.confirmedBookings += 1;
        } else if (booking.booking_status === 'cancelled') {
          perf.cancelledBookings += 1;
        }
      });

      // Calculate averages and rates
      Object.values(roomPerformance).forEach(perf => {
        perf.averageStayLength = perf.confirmedBookings > 0 
          ? perf.totalNights / perf.confirmedBookings 
          : 0;
        perf.cancellationRate = perf.totalBookings > 0 
          ? (perf.cancelledBookings / perf.totalBookings) * 100 
          : 0;
        perf.averageRevenuePerBooking = perf.confirmedBookings > 0 
          ? perf.totalRevenue / perf.confirmedBookings 
          : 0;
      });

      const sortedRooms = Object.entries(roomPerformance)
        .map(([roomId, data]) => ({ roomId, ...data }))
        .sort((a, b) => b.totalRevenue - a.totalRevenue);

      return { data: sortedRooms, error: null };
    } catch (error) {
      console.error('Error fetching room performance:', error);
      return { data: null, error };
    }
  }
};
