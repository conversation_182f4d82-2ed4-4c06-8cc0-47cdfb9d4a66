// Review service for managing guest reviews and ratings

import { supabase, TABLES } from './supabase';

export const reviewService = {
  // Create a new review
  createReview: async (reviewData) => {
    try {
      const review = {
        booking_id: reviewData.bookingId,
        guest_id: reviewData.guestId,
        room_id: reviewData.roomId,
        overall_rating: reviewData.overallRating,
        cleanliness_rating: reviewData.cleanlinessRating,
        comfort_rating: reviewData.comfortRating,
        service_rating: reviewData.serviceRating,
        value_rating: reviewData.valueRating,
        review_text: reviewData.reviewText,
        would_recommend: reviewData.wouldRecommend
      };

      const { data, error } = await supabase
        .from(TABLES.REVIEWS)
        .insert(review)
        .select(`
          *,
          guest:profiles(full_name),
          room:rooms(room_number, room_type),
          booking:bookings(check_in_date, check_out_date)
        `)
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error creating review:', error);
      return { data: null, error };
    }
  },

  // Get reviews for a specific room
  getRoomReviews: async (roomId, limit = 10, offset = 0) => {
    try {
      const { data, error } = await supabase
        .from(TABLES.REVIEWS)
        .select(`
          *,
          guest:profiles(full_name),
          booking:bookings(check_in_date, check_out_date)
        `)
        .eq('room_id', roomId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching room reviews:', error);
      return { data: null, error };
    }
  },

  // Get all reviews (admin)
  getAllReviews: async (filters = {}) => {
    try {
      let query = supabase
        .from(TABLES.REVIEWS)
        .select(`
          *,
          guest:profiles(full_name, email),
          room:rooms(room_number, room_type),
          booking:bookings(check_in_date, check_out_date)
        `);

      // Apply filters
      if (filters.roomId) {
        query = query.eq('room_id', filters.roomId);
      }

      if (filters.minRating) {
        query = query.gte('overall_rating', filters.minRating);
      }

      if (filters.maxRating) {
        query = query.lte('overall_rating', filters.maxRating);
      }

      if (filters.startDate) {
        query = query.gte('created_at', filters.startDate);
      }

      if (filters.endDate) {
        query = query.lte('created_at', filters.endDate);
      }

      query = query.order('created_at', { ascending: false });

      const { data, error } = await query;

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching all reviews:', error);
      return { data: null, error };
    }
  },

  // Get review by booking ID
  getReviewByBooking: async (bookingId) => {
    try {
      const { data, error } = await supabase
        .from(TABLES.REVIEWS)
        .select(`
          *,
          guest:profiles(full_name),
          room:rooms(room_number, room_type),
          booking:bookings(check_in_date, check_out_date)
        `)
        .eq('booking_id', bookingId)
        .single();

      if (error && error.code !== 'PGRST116') throw error; // PGRST116 = no rows returned
      return { data: error?.code === 'PGRST116' ? null : data, error: null };
    } catch (error) {
      console.error('Error fetching review by booking:', error);
      return { data: null, error };
    }
  },

  // Get user's reviews
  getUserReviews: async (userId) => {
    try {
      const { data, error } = await supabase
        .from(TABLES.REVIEWS)
        .select(`
          *,
          room:rooms(room_number, room_type),
          booking:bookings(check_in_date, check_out_date)
        `)
        .eq('guest_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching user reviews:', error);
      return { data: null, error };
    }
  },

  // Get room rating summary
  getRoomRatingSummary: async (roomId) => {
    try {
      const { data, error } = await supabase
        .from(TABLES.REVIEWS)
        .select('overall_rating, cleanliness_rating, comfort_rating, service_rating, value_rating, would_recommend')
        .eq('room_id', roomId);

      if (error) throw error;

      if (data.length === 0) {
        return {
          data: {
            averageRating: 0,
            totalReviews: 0,
            ratingBreakdown: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 },
            categoryAverages: {
              cleanliness: 0,
              comfort: 0,
              service: 0,
              value: 0
            },
            recommendationRate: 0
          },
          error: null
        };
      }

      // Calculate averages
      const totalReviews = data.length;
      const averageRating = data.reduce((sum, review) => sum + review.overall_rating, 0) / totalReviews;

      // Rating breakdown
      const ratingBreakdown = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 };
      data.forEach(review => {
        ratingBreakdown[review.overall_rating] += 1;
      });

      // Category averages
      const categoryAverages = {
        cleanliness: data.reduce((sum, review) => sum + review.cleanliness_rating, 0) / totalReviews,
        comfort: data.reduce((sum, review) => sum + review.comfort_rating, 0) / totalReviews,
        service: data.reduce((sum, review) => sum + review.service_rating, 0) / totalReviews,
        value: data.reduce((sum, review) => sum + review.value_rating, 0) / totalReviews
      };

      // Recommendation rate
      const recommendationRate = (data.filter(review => review.would_recommend).length / totalReviews) * 100;

      return {
        data: {
          averageRating,
          totalReviews,
          ratingBreakdown,
          categoryAverages,
          recommendationRate
        },
        error: null
      };
    } catch (error) {
      console.error('Error fetching room rating summary:', error);
      return { data: null, error };
    }
  },

  // Get overall guesthouse rating summary
  getOverallRatingSummary: async () => {
    try {
      const { data, error } = await supabase
        .from(TABLES.REVIEWS)
        .select('overall_rating, cleanliness_rating, comfort_rating, service_rating, value_rating, would_recommend');

      if (error) throw error;

      if (data.length === 0) {
        return {
          data: {
            averageRating: 0,
            totalReviews: 0,
            ratingBreakdown: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 },
            categoryAverages: {
              cleanliness: 0,
              comfort: 0,
              service: 0,
              value: 0
            },
            recommendationRate: 0
          },
          error: null
        };
      }

      // Calculate averages
      const totalReviews = data.length;
      const averageRating = data.reduce((sum, review) => sum + review.overall_rating, 0) / totalReviews;

      // Rating breakdown
      const ratingBreakdown = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 };
      data.forEach(review => {
        ratingBreakdown[review.overall_rating] += 1;
      });

      // Category averages
      const categoryAverages = {
        cleanliness: data.reduce((sum, review) => sum + review.cleanliness_rating, 0) / totalReviews,
        comfort: data.reduce((sum, review) => sum + review.comfort_rating, 0) / totalReviews,
        service: data.reduce((sum, review) => sum + review.service_rating, 0) / totalReviews,
        value: data.reduce((sum, review) => sum + review.value_rating, 0) / totalReviews
      };

      // Recommendation rate
      const recommendationRate = (data.filter(review => review.would_recommend).length / totalReviews) * 100;

      return {
        data: {
          averageRating,
          totalReviews,
          ratingBreakdown,
          categoryAverages,
          recommendationRate
        },
        error: null
      };
    } catch (error) {
      console.error('Error fetching overall rating summary:', error);
      return { data: null, error };
    }
  },

  // Check if user can review a booking
  canReviewBooking: async (bookingId, userId) => {
    try {
      // Check if booking exists and belongs to user
      const { data: booking, error: bookingError } = await supabase
        .from(TABLES.BOOKINGS)
        .select('guest_id, booking_status, check_out_date')
        .eq('id', bookingId)
        .eq('guest_id', userId)
        .single();

      if (bookingError) return { canReview: false, reason: 'Booking not found' };

      // Check if booking is completed
      if (booking.booking_status !== 'completed') {
        return { canReview: false, reason: 'Booking must be completed to leave a review' };
      }

      // Check if check-out date has passed
      const checkOutDate = new Date(booking.check_out_date);
      const now = new Date();
      if (checkOutDate > now) {
        return { canReview: false, reason: 'Cannot review before check-out date' };
      }

      // Check if review already exists
      const { data: existingReview } = await supabase
        .from(TABLES.REVIEWS)
        .select('id')
        .eq('booking_id', bookingId)
        .single();

      if (existingReview) {
        return { canReview: false, reason: 'Review already submitted for this booking' };
      }

      return { canReview: true, reason: null };
    } catch (error) {
      console.error('Error checking review eligibility:', error);
      return { canReview: false, reason: 'Error checking eligibility' };
    }
  },

  // Update review
  updateReview: async (reviewId, updates) => {
    try {
      const { data, error } = await supabase
        .from(TABLES.REVIEWS)
        .update(updates)
        .eq('id', reviewId)
        .select(`
          *,
          guest:profiles(full_name),
          room:rooms(room_number, room_type),
          booking:bookings(check_in_date, check_out_date)
        `)
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error updating review:', error);
      return { data: null, error };
    }
  },

  // Delete review
  deleteReview: async (reviewId) => {
    try {
      const { error } = await supabase
        .from(TABLES.REVIEWS)
        .delete()
        .eq('id', reviewId);

      if (error) throw error;
      return { success: true, error: null };
    } catch (error) {
      console.error('Error deleting review:', error);
      return { success: false, error };
    }
  },

  // Get recent reviews for homepage
  getRecentReviews: async (limit = 6) => {
    try {
      const { data, error } = await supabase
        .from(TABLES.REVIEWS)
        .select(`
          overall_rating,
          review_text,
          created_at,
          guest:profiles(full_name),
          room:rooms(room_type)
        `)
        .gte('overall_rating', 4) // Only show good reviews on homepage
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching recent reviews:', error);
      return { data: null, error };
    }
  }
};
