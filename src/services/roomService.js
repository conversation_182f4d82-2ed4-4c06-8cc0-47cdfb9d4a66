// Room service for managing room data and availability

import { supabase, db, TABLES } from './supabase';
import { formatDateForAPI } from '../utils/date';

export const roomService = {
  // Get all available rooms
  getAllRooms: async () => {
    try {
      const { data, error } = await supabase
        .from(TABLES.ROOMS)
        .select('*')
        .eq('is_available', true)
        .order('room_number');

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching rooms:', error);
      return { data: null, error };
    }
  },

  // Get room by ID
  getRoomById: async (roomId) => {
    try {
      const { data, error } = await supabase
        .from(TABLES.ROOMS)
        .select('*')
        .eq('id', roomId)
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching room:', error);
      return { data: null, error };
    }
  },

  // Search rooms with filters
  searchRooms: async (filters = {}) => {
    try {
      let query = supabase
        .from(TABLES.ROOMS)
        .select('*')
        .eq('is_available', true);

      // Apply filters
      if (filters.roomType && filters.roomType !== 'all') {
        query = query.eq('room_type', filters.roomType);
      }

      if (filters.minPrice) {
        query = query.gte('price_per_night', filters.minPrice);
      }

      if (filters.maxPrice) {
        query = query.lte('price_per_night', filters.maxPrice);
      }

      if (filters.capacity) {
        query = query.gte('capacity', filters.capacity);
      }

      if (filters.amenities && filters.amenities.length > 0) {
        // Check if room has all required amenities
        filters.amenities.forEach(amenity => {
          query = query.contains('amenities', [amenity]);
        });
      }

      query = query.order('price_per_night');

      const { data, error } = await query;

      if (error) throw error;

      // If date range is provided, check availability
      if (filters.checkIn && filters.checkOut) {
        const availableRooms = await Promise.all(
          data.map(async (room) => {
            const isAvailable = await roomService.checkAvailability(
              room.id,
              filters.checkIn,
              filters.checkOut
            );
            return isAvailable.available ? room : null;
          })
        );

        return { 
          data: availableRooms.filter(room => room !== null), 
          error: null 
        };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Error searching rooms:', error);
      return { data: null, error };
    }
  },

  // Check room availability for date range
  checkAvailability: async (roomId, checkIn, checkOut) => {
    try {
      const checkInDate = formatDateForAPI(checkIn);
      const checkOutDate = formatDateForAPI(checkOut);

      const { data, error } = await supabase
        .from(TABLES.BOOKINGS)
        .select('id')
        .eq('room_id', roomId)
        .in('booking_status', ['confirmed', 'pending'])
        .or(`and(check_in_date.lte.${checkInDate},check_out_date.gt.${checkInDate}),and(check_in_date.lt.${checkOutDate},check_out_date.gte.${checkOutDate}),and(check_in_date.gte.${checkInDate},check_out_date.lte.${checkOutDate})`);

      if (error) throw error;

      return { 
        available: data.length === 0, 
        conflictingBookings: data.length,
        error: null 
      };
    } catch (error) {
      console.error('Error checking availability:', error);
      return { available: false, error };
    }
  },

  // Get available rooms for specific date range
  getAvailableRooms: async (checkIn, checkOut, filters = {}) => {
    try {
      // First get all rooms matching basic filters
      const { data: rooms, error: roomError } = await roomService.searchRooms({
        ...filters,
        checkIn: null, // Don't apply date filter in initial search
        checkOut: null
      });

      if (roomError) throw roomError;

      // Then check availability for each room
      const availableRooms = await Promise.all(
        rooms.map(async (room) => {
          const availability = await roomService.checkAvailability(
            room.id,
            checkIn,
            checkOut
          );
          
          return availability.available ? room : null;
        })
      );

      return { 
        data: availableRooms.filter(room => room !== null), 
        error: null 
      };
    } catch (error) {
      console.error('Error getting available rooms:', error);
      return { data: null, error };
    }
  },

  // Get room types for filter dropdown
  getRoomTypes: async () => {
    try {
      const { data, error } = await supabase
        .from(TABLES.ROOMS)
        .select('room_type')
        .eq('is_available', true);

      if (error) throw error;

      // Get unique room types
      const uniqueTypes = [...new Set(data.map(room => room.room_type))];
      
      return { data: uniqueTypes, error: null };
    } catch (error) {
      console.error('Error fetching room types:', error);
      return { data: null, error };
    }
  },

  // Get price range for filter
  getPriceRange: async () => {
    try {
      const { data, error } = await supabase
        .from(TABLES.ROOMS)
        .select('price_per_night')
        .eq('is_available', true);

      if (error) throw error;

      const prices = data.map(room => room.price_per_night);
      const minPrice = Math.min(...prices);
      const maxPrice = Math.max(...prices);

      return { 
        data: { minPrice, maxPrice }, 
        error: null 
      };
    } catch (error) {
      console.error('Error fetching price range:', error);
      return { data: null, error };
    }
  },

  // Get all available amenities
  getAvailableAmenities: async () => {
    try {
      const { data, error } = await supabase
        .from(TABLES.ROOMS)
        .select('amenities')
        .eq('is_available', true);

      if (error) throw error;

      // Flatten and get unique amenities
      const allAmenities = data.reduce((acc, room) => {
        if (room.amenities && Array.isArray(room.amenities)) {
          return [...acc, ...room.amenities];
        }
        return acc;
      }, []);

      const uniqueAmenities = [...new Set(allAmenities)];

      return { data: uniqueAmenities, error: null };
    } catch (error) {
      console.error('Error fetching amenities:', error);
      return { data: null, error };
    }
  }
};
