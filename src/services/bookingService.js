// Booking service for managing reservations

import { supabase, db, TABLES } from './supabase';
import { formatDateForAPI } from '../utils/date';

export const bookingService = {
  // Create a new booking
  createBooking: async (bookingData) => {
    try {
      const booking = {
        guest_id: bookingData.guestId,
        room_id: bookingData.roomId,
        check_in_date: formatDateForAPI(bookingData.checkIn),
        check_out_date: formatDateForAPI(bookingData.checkOut),
        total_amount: bookingData.totalAmount,
        guest_count: bookingData.guestCount,
        special_requests: bookingData.specialRequests || null,
        payment_status: 'pending',
        booking_status: 'confirmed'
      };

      const { data, error } = await supabase
        .from(TABLES.BOOKINGS)
        .insert(booking)
        .select(`
          *,
          room:rooms(*),
          guest:profiles(*)
        `)
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error creating booking:', error);
      return { data: null, error };
    }
  },

  // Get user's bookings
  getUserBookings: async (userId) => {
    try {
      const { data, error } = await supabase
        .from(TABLES.BOOKINGS)
        .select(`
          *,
          room:rooms(*),
          payments(*)
        `)
        .eq('guest_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching user bookings:', error);
      return { data: null, error };
    }
  },

  // Get booking by ID
  getBookingById: async (bookingId) => {
    try {
      const { data, error } = await supabase
        .from(TABLES.BOOKINGS)
        .select(`
          *,
          room:rooms(*),
          guest:profiles(*),
          payments(*)
        `)
        .eq('id', bookingId)
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching booking:', error);
      return { data: null, error };
    }
  },

  // Update booking status
  updateBookingStatus: async (bookingId, status) => {
    try {
      const { data, error } = await supabase
        .from(TABLES.BOOKINGS)
        .update({ booking_status: status })
        .eq('id', bookingId)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error updating booking status:', error);
      return { data: null, error };
    }
  },

  // Cancel booking
  cancelBooking: async (bookingId, reason = null) => {
    try {
      const { data, error } = await supabase
        .from(TABLES.BOOKINGS)
        .update({ 
          booking_status: 'cancelled',
          special_requests: reason ? `Cancelled: ${reason}` : 'Cancelled by guest'
        })
        .eq('id', bookingId)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error cancelling booking:', error);
      return { data: null, error };
    }
  },

  // Get all bookings (admin only)
  getAllBookings: async (filters = {}) => {
    try {
      let query = supabase
        .from(TABLES.BOOKINGS)
        .select(`
          *,
          room:rooms(*),
          guest:profiles(*),
          payments(*)
        `);

      // Apply filters
      if (filters.status) {
        query = query.eq('booking_status', filters.status);
      }

      if (filters.roomId) {
        query = query.eq('room_id', filters.roomId);
      }

      if (filters.startDate) {
        query = query.gte('check_in_date', formatDateForAPI(filters.startDate));
      }

      if (filters.endDate) {
        query = query.lte('check_out_date', formatDateForAPI(filters.endDate));
      }

      query = query.order('created_at', { ascending: false });

      const { data, error } = await query;

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching all bookings:', error);
      return { data: null, error };
    }
  },

  // Get booking statistics
  getBookingStats: async (startDate = null, endDate = null) => {
    try {
      let query = supabase
        .from(TABLES.BOOKINGS)
        .select('*');

      if (startDate) {
        query = query.gte('check_in_date', formatDateForAPI(startDate));
      }

      if (endDate) {
        query = query.lte('check_out_date', formatDateForAPI(endDate));
      }

      const { data, error } = await query;

      if (error) throw error;

      // Calculate statistics
      const stats = {
        totalBookings: data.length,
        confirmedBookings: data.filter(b => b.booking_status === 'confirmed').length,
        cancelledBookings: data.filter(b => b.booking_status === 'cancelled').length,
        completedBookings: data.filter(b => b.booking_status === 'completed').length,
        totalRevenue: data
          .filter(b => b.payment_status === 'paid')
          .reduce((sum, b) => sum + parseFloat(b.total_amount), 0),
        pendingRevenue: data
          .filter(b => b.payment_status === 'pending' && b.booking_status === 'confirmed')
          .reduce((sum, b) => sum + parseFloat(b.total_amount), 0)
      };

      return { data: stats, error: null };
    } catch (error) {
      console.error('Error fetching booking stats:', error);
      return { data: null, error };
    }
  },

  // Get upcoming check-ins (for admin dashboard)
  getUpcomingCheckIns: async (days = 7) => {
    try {
      const today = new Date();
      const futureDate = new Date();
      futureDate.setDate(today.getDate() + days);

      const { data, error } = await supabase
        .from(TABLES.BOOKINGS)
        .select(`
          *,
          room:rooms(*),
          guest:profiles(*)
        `)
        .eq('booking_status', 'confirmed')
        .gte('check_in_date', formatDateForAPI(today))
        .lte('check_in_date', formatDateForAPI(futureDate))
        .order('check_in_date');

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching upcoming check-ins:', error);
      return { data: null, error };
    }
  },

  // Get upcoming check-outs (for admin dashboard)
  getUpcomingCheckOuts: async (days = 7) => {
    try {
      const today = new Date();
      const futureDate = new Date();
      futureDate.setDate(today.getDate() + days);

      const { data, error } = await supabase
        .from(TABLES.BOOKINGS)
        .select(`
          *,
          room:rooms(*),
          guest:profiles(*)
        `)
        .in('booking_status', ['confirmed', 'completed'])
        .gte('check_out_date', formatDateForAPI(today))
        .lte('check_out_date', formatDateForAPI(futureDate))
        .order('check_out_date');

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching upcoming check-outs:', error);
      return { data: null, error };
    }
  },

  // Check for booking conflicts
  checkBookingConflicts: async (roomId, checkIn, checkOut, excludeBookingId = null) => {
    try {
      let query = supabase
        .from(TABLES.BOOKINGS)
        .select('id, check_in_date, check_out_date')
        .eq('room_id', roomId)
        .in('booking_status', ['confirmed', 'pending']);

      if (excludeBookingId) {
        query = query.neq('id', excludeBookingId);
      }

      const { data, error } = await query;

      if (error) throw error;

      const checkInDate = new Date(checkIn);
      const checkOutDate = new Date(checkOut);

      const conflicts = data.filter(booking => {
        const existingCheckIn = new Date(booking.check_in_date);
        const existingCheckOut = new Date(booking.check_out_date);

        return (
          (checkInDate >= existingCheckIn && checkInDate < existingCheckOut) ||
          (checkOutDate > existingCheckIn && checkOutDate <= existingCheckOut) ||
          (checkInDate <= existingCheckIn && checkOutDate >= existingCheckOut)
        );
      });

      return { 
        hasConflicts: conflicts.length > 0, 
        conflicts,
        error: null 
      };
    } catch (error) {
      console.error('Error checking booking conflicts:', error);
      return { hasConflicts: true, error };
    }
  }
};
