// Setup verification utility to test Supabase connection and database

import { supabase, TABLES } from '../services/supabase';

export const setupVerification = {
  // Test basic connection
  testConnection: async () => {
    try {
      const { data, error } = await supabase.from('profiles').select('count').limit(1);
      
      if (error) {
        console.error('Connection test failed:', error);
        return { success: false, error: error.message };
      }
      
      console.log('✅ Supabase connection successful');
      return { success: true };
    } catch (error) {
      console.error('Connection test error:', error);
      return { success: false, error: error.message };
    }
  },

  // Test all required tables exist
  testTables: async () => {
    const results = {};
    
    for (const [tableName, tableKey] of Object.entries(TABLES)) {
      try {
        const { data, error } = await supabase
          .from(tableKey)
          .select('count')
          .limit(1);
        
        if (error) {
          console.error(`❌ Table ${tableKey} test failed:`, error.message);
          results[tableName] = { success: false, error: error.message };
        } else {
          console.log(`✅ Table ${tableKey} exists`);
          results[tableName] = { success: true };
        }
      } catch (error) {
        console.error(`❌ Table ${tableKey} error:`, error);
        results[tableName] = { success: false, error: error.message };
      }
    }
    
    return results;
  },

  // Test authentication
  testAuth: async () => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        console.error('Auth test failed:', error);
        return { success: false, error: error.message };
      }
      
      console.log('✅ Authentication system working');
      console.log('Current session:', session ? 'Logged in' : 'Not logged in');
      
      return { success: true, session };
    } catch (error) {
      console.error('Auth test error:', error);
      return { success: false, error: error.message };
    }
  },

  // Run all tests
  runAllTests: async () => {
    console.log('🔍 Running Supabase setup verification...');
    
    const results = {
      connection: await setupVerification.testConnection(),
      tables: await setupVerification.testTables(),
      auth: await setupVerification.testAuth()
    };
    
    // Summary
    const connectionOk = results.connection.success;
    const tablesOk = Object.values(results.tables).every(t => t.success);
    const authOk = results.auth.success;
    
    console.log('\n📊 Setup Verification Summary:');
    console.log(`Connection: ${connectionOk ? '✅' : '❌'}`);
    console.log(`Tables: ${tablesOk ? '✅' : '❌'}`);
    console.log(`Authentication: ${authOk ? '✅' : '❌'}`);
    
    if (connectionOk && tablesOk && authOk) {
      console.log('\n🎉 All systems ready! Your Supabase setup is complete.');
    } else {
      console.log('\n⚠️  Some issues detected. Please check the errors above.');
      
      if (!connectionOk) {
        console.log('- Check your VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY in .env');
      }
      
      if (!tablesOk) {
        console.log('- Run the database schema from database/schema.sql in Supabase SQL Editor');
      }
      
      if (!authOk) {
        console.log('- Check your Supabase authentication settings');
      }
    }
    
    return {
      allGood: connectionOk && tablesOk && authOk,
      details: results
    };
  },

  // Create sample admin user (for development)
  createSampleData: async () => {
    try {
      console.log('🔧 Creating sample data...');
      
      // This would typically be done through the Supabase dashboard
      // but we can provide instructions
      console.log('To create an admin user:');
      console.log('1. Register through the app');
      console.log('2. Go to Supabase Dashboard → Authentication → Users');
      console.log('3. Edit your user and add to Raw User Meta Data:');
      console.log('   {"role": "admin", "full_name": "Your Name"}');
      
      return { success: true };
    } catch (error) {
      console.error('Sample data creation error:', error);
      return { success: false, error: error.message };
    }
  }
};

// Auto-run verification in development
if (import.meta.env.DEV) {
  // Run verification after a short delay to ensure everything is loaded
  setTimeout(() => {
    setupVerification.runAllTests();
  }, 2000);
}

export default setupVerification;
