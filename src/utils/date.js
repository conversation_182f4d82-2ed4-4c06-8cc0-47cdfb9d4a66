// Date utility functions for the guesthouse system

import { format, parseISO, differenceInDays, addDays, isAfter, isBefore, isEqual } from 'date-fns';
import { DATE_FORMATS } from '../constants';

/**
 * Format date for display
 * @param {Date|string} date - Date to format
 * @param {string} formatString - Format string (optional)
 * @returns {string} Formatted date
 */
export const formatDate = (date, formatString = DATE_FORMATS.DISPLAY) => {
  if (!date) return '';
  
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return format(dateObj, formatString);
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
};

/**
 * Format date for API calls
 * @param {Date|string} date - Date to format
 * @returns {string} API formatted date (YYYY-MM-DD)
 */
export const formatDateForAPI = (date) => {
  return formatDate(date, DATE_FORMATS.API);
};

/**
 * Calculate number of nights between check-in and check-out
 * @param {Date|string} checkIn - Check-in date
 * @param {Date|string} checkOut - Check-out date
 * @returns {number} Number of nights
 */
export const calculateNights = (checkIn, checkOut) => {
  if (!checkIn || !checkOut) return 0;
  
  try {
    const checkInDate = typeof checkIn === 'string' ? parseISO(checkIn) : checkIn;
    const checkOutDate = typeof checkOut === 'string' ? parseISO(checkOut) : checkOut;
    
    const nights = differenceInDays(checkOutDate, checkInDate);
    return nights > 0 ? nights : 0;
  } catch (error) {
    console.error('Error calculating nights:', error);
    return 0;
  }
};

/**
 * Check if a date is in the future
 * @param {Date|string} date - Date to check
 * @returns {boolean} True if date is in the future
 */
export const isFutureDate = (date) => {
  if (!date) return false;
  
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return isAfter(dateObj, new Date());
  } catch (error) {
    console.error('Error checking future date:', error);
    return false;
  }
};

/**
 * Check if a date is today
 * @param {Date|string} date - Date to check
 * @returns {boolean} True if date is today
 */
export const isToday = (date) => {
  if (!date) return false;
  
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    const today = new Date();
    return isEqual(
      new Date(dateObj.getFullYear(), dateObj.getMonth(), dateObj.getDate()),
      new Date(today.getFullYear(), today.getMonth(), today.getDate())
    );
  } catch (error) {
    console.error('Error checking if today:', error);
    return false;
  }
};

/**
 * Get minimum check-in date (today)
 * @returns {Date} Today's date
 */
export const getMinCheckInDate = () => {
  return new Date();
};

/**
 * Get minimum check-out date (day after check-in)
 * @param {Date|string} checkInDate - Check-in date
 * @returns {Date} Minimum check-out date
 */
export const getMinCheckOutDate = (checkInDate) => {
  if (!checkInDate) return addDays(new Date(), 1);
  
  try {
    const checkIn = typeof checkInDate === 'string' ? parseISO(checkInDate) : checkInDate;
    return addDays(checkIn, 1);
  } catch (error) {
    console.error('Error getting min checkout date:', error);
    return addDays(new Date(), 1);
  }
};

/**
 * Validate date range for booking
 * @param {Date|string} checkIn - Check-in date
 * @param {Date|string} checkOut - Check-out date
 * @returns {object} Validation result
 */
export const validateDateRange = (checkIn, checkOut) => {
  const errors = [];
  
  if (!checkIn) {
    errors.push('Check-in date is required');
  } else if (!isFutureDate(checkIn) && !isToday(checkIn)) {
    errors.push('Check-in date must be today or in the future');
  }
  
  if (!checkOut) {
    errors.push('Check-out date is required');
  }
  
  if (checkIn && checkOut) {
    try {
      const checkInDate = typeof checkIn === 'string' ? parseISO(checkIn) : checkIn;
      const checkOutDate = typeof checkOut === 'string' ? parseISO(checkOut) : checkOut;
      
      if (!isAfter(checkOutDate, checkInDate)) {
        errors.push('Check-out date must be after check-in date');
      }
      
      const nights = calculateNights(checkIn, checkOut);
      if (nights > 30) {
        errors.push('Maximum stay is 30 nights');
      }
    } catch (error) {
      errors.push('Invalid date format');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    nights: errors.length === 0 ? calculateNights(checkIn, checkOut) : 0
  };
};

/**
 * Generate date range array
 * @param {Date|string} startDate - Start date
 * @param {Date|string} endDate - End date
 * @returns {Date[]} Array of dates in range
 */
export const generateDateRange = (startDate, endDate) => {
  const dates = [];
  
  try {
    const start = typeof startDate === 'string' ? parseISO(startDate) : startDate;
    const end = typeof endDate === 'string' ? parseISO(endDate) : endDate;
    
    let currentDate = start;
    while (isBefore(currentDate, end) || isEqual(currentDate, end)) {
      dates.push(new Date(currentDate));
      currentDate = addDays(currentDate, 1);
    }
  } catch (error) {
    console.error('Error generating date range:', error);
  }
  
  return dates;
};

/**
 * Format date range for display
 * @param {Date|string} startDate - Start date
 * @param {Date|string} endDate - End date
 * @returns {string} Formatted date range
 */
export const formatDateRange = (startDate, endDate) => {
  if (!startDate || !endDate) return '';
  
  const start = formatDate(startDate);
  const end = formatDate(endDate);
  const nights = calculateNights(startDate, endDate);
  
  return `${start} - ${end} (${nights} night${nights !== 1 ? 's' : ''})`;
};
