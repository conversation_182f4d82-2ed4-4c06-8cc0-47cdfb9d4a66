// Currency utility functions for Ghana Cedis

import { CURRENCY } from '../constants';

/**
 * Format amount to Ghana Cedis currency
 * @param {number} amount - The amount to format
 * @param {boolean} showSymbol - Whether to show currency symbol
 * @returns {string} Formatted currency string
 */
export const formatCurrency = (amount, showSymbol = true) => {
  if (typeof amount !== 'number' || isNaN(amount)) {
    return showSymbol ? `${CURRENCY.SYMBOL}0.00` : '0.00';
  }

  const formatted = new Intl.NumberFormat('en-GH', {
    style: 'currency',
    currency: CURRENCY.CODE,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);

  // If we don't want the symbol, remove it
  if (!showSymbol) {
    return formatted.replace(CURRENCY.SYMBOL, '').trim();
  }

  return formatted;
};

/**
 * Parse currency string to number
 * @param {string} currencyString - Currency string to parse
 * @returns {number} Parsed amount
 */
export const parseCurrency = (currencyString) => {
  if (!currencyString) return 0;
  
  // Remove currency symbol and any non-numeric characters except decimal point
  const cleanString = currencyString
    .replace(CURRENCY.SYMBOL, '')
    .replace(/[^\d.-]/g, '');
  
  const parsed = parseFloat(cleanString);
  return isNaN(parsed) ? 0 : parsed;
};

/**
 * Calculate total with tax (if applicable)
 * @param {number} amount - Base amount
 * @param {number} taxRate - Tax rate (default 0 for Ghana)
 * @returns {number} Total amount
 */
export const calculateTotal = (amount, taxRate = 0) => {
  if (typeof amount !== 'number' || isNaN(amount)) return 0;
  return amount + (amount * taxRate);
};

/**
 * Calculate booking total for multiple nights
 * @param {number} pricePerNight - Price per night
 * @param {number} nights - Number of nights
 * @param {number} discount - Discount percentage (0-100)
 * @returns {object} Breakdown of costs
 */
export const calculateBookingTotal = (pricePerNight, nights, discount = 0) => {
  const subtotal = pricePerNight * nights;
  const discountAmount = (subtotal * discount) / 100;
  const total = subtotal - discountAmount;

  return {
    pricePerNight,
    nights,
    subtotal,
    discount,
    discountAmount,
    total,
    formatted: {
      pricePerNight: formatCurrency(pricePerNight),
      subtotal: formatCurrency(subtotal),
      discountAmount: formatCurrency(discountAmount),
      total: formatCurrency(total)
    }
  };
};

/**
 * Format currency for display in tables/lists
 * @param {number} amount - Amount to format
 * @returns {string} Compact currency format
 */
export const formatCurrencyCompact = (amount) => {
  if (typeof amount !== 'number' || isNaN(amount)) return `${CURRENCY.SYMBOL}0`;

  if (amount >= 1000000) {
    return `${CURRENCY.SYMBOL}${(amount / 1000000).toFixed(1)}M`;
  } else if (amount >= 1000) {
    return `${CURRENCY.SYMBOL}${(amount / 1000).toFixed(1)}K`;
  }

  return formatCurrency(amount);
};

/**
 * Validate currency amount
 * @param {string|number} amount - Amount to validate
 * @returns {object} Validation result
 */
export const validateCurrencyAmount = (amount) => {
  const numAmount = typeof amount === 'string' ? parseCurrency(amount) : amount;
  
  return {
    isValid: typeof numAmount === 'number' && numAmount >= 0 && !isNaN(numAmount),
    amount: numAmount,
    formatted: formatCurrency(numAmount)
  };
};
