/* Professional Hotel Booking App CSS */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

/* CSS Variables - Luxury Hotel Theme */
:root {
  /* Primary Colors - Elegant Navy & Gold */
  --primary-dark: #1a2332;
  --primary-navy: #2c3e50;
  --primary-blue: #34495e;
  --primary-light: #ecf0f1;

  /* Accent Colors - Luxury Gold */
  --accent-gold: #d4af37;
  --accent-gold-light: #f1c40f;
  --accent-gold-dark: #b8860b;

  /* Secondary Colors - Warm Grays */
  --secondary-dark: #2c3e50;
  --secondary-medium: #7f8c8d;
  --secondary-light: #bdc3c7;
  --secondary-lighter: #ecf0f1;

  /* Neutral Colors - Sophisticated Grays */
  --white: #ffffff;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Semantic Colors */
  --success: #059669;
  --success-light: #d1fae5;
  --warning: #d97706;
  --warning-light: #fef3c7;
  --error: #dc2626;
  --error-light: #fee2e2;
  --info: #0284c7;
  --info-light: #dbeafe;

  /* Typography */
  --font-heading: 'Playfair Display', serif;
  --font-body: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

  /* Font Sizes - Responsive Scale */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;

  /* Spacing System */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  --space-32: 8rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-3xl: 2rem;
  --radius-full: 9999px;

  /* Luxury Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(15, 23, 42, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(15, 23, 42, 0.1), 0 2px 4px -1px rgba(15, 23, 42, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(15, 23, 42, 0.1), 0 4px 6px -2px rgba(15, 23, 42, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(15, 23, 42, 0.1), 0 10px 10px -5px rgba(15, 23, 42, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(15, 23, 42, 0.25);
  --shadow-gold: 0 4px 14px 0 rgba(212, 175, 55, 0.2);

  /* Smooth Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* CSS Reset & Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  height: 100%;
}

body {
  font-family: var(--font-body);
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--gray-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
}

#root {
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
}

/* Typography Styles */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 600;
  line-height: 1.2;
  color: var(--primary-dark);
  margin-bottom: var(--space-4);
}

h1 {
  font-size: var(--text-4xl);
  font-weight: 700;
  letter-spacing: -0.025em;
}

h2 {
  font-size: var(--text-3xl);
  font-weight: 600;
  letter-spacing: -0.025em;
}

h3 {
  font-size: var(--text-2xl);
  font-weight: 600;
}

h4 {
  font-size: var(--text-xl);
  font-weight: 500;
}

h5 {
  font-size: var(--text-lg);
  font-weight: 500;
}

h6 {
  font-size: var(--text-base);
  font-weight: 500;
}

p {
  margin-bottom: var(--space-4);
  color: var(--gray-600);
  line-height: 1.7;
}

/* Links */
a {
  color: var(--primary-navy);
  text-decoration: none;
  transition: all var(--transition-fast);
}

a:hover {
  color: var(--accent-gold);
}

/* Layout Utilities */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }
.flex-1 { flex: 1 1 0%; }

.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* Professional Hotel Navigation */
.hotel-navbar {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-navy) 100%);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(212, 175, 55, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: var(--shadow-lg);
}

.hotel-navbar .container {
  padding: var(--space-4) var(--space-4);
}

.hotel-brand {
  font-family: var(--font-heading);
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--white);
  text-decoration: none;
  letter-spacing: -0.025em;
}

.hotel-brand:hover {
  color: var(--accent-gold);
}

.hotel-nav {
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.hotel-nav-link {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  font-size: var(--text-sm);
  text-decoration: none;
  transition: all var(--transition-normal);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
}

.hotel-nav-link:hover {
  color: var(--accent-gold);
  background: rgba(212, 175, 55, 0.1);
}

.hotel-nav-link.active {
  color: var(--accent-gold);
  font-weight: 600;
}

/* Luxury Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-family: var(--font-body);
  font-size: var(--text-sm);
  font-weight: 600;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  white-space: nowrap;
  user-select: none;
  outline: none;
  min-height: 2.75rem;
  letter-spacing: 0.025em;
}

.btn:focus {
  outline: 2px solid var(--accent-gold);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* Primary Button - Luxury Gold */
.btn-primary {
  background: linear-gradient(135deg, var(--accent-gold) 0%, var(--accent-gold-dark) 100%);
  color: var(--white);
  border-color: var(--accent-gold);
  box-shadow: var(--shadow-gold);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--accent-gold-dark) 0%, var(--accent-gold) 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl), var(--shadow-gold);
}

/* Secondary Button - Navy */
.btn-secondary {
  background: var(--primary-navy);
  color: var(--white);
  border-color: var(--primary-navy);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Outline Button */
.btn-outline {
  background: transparent;
  color: var(--primary-navy);
  border-color: var(--primary-navy);
}

.btn-outline:hover:not(:disabled) {
  background: var(--primary-navy);
  color: var(--white);
  transform: translateY(-2px);
}

/* Ghost Button */
.btn-ghost {
  background: transparent;
  color: var(--gray-600);
  border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
  background: var(--gray-100);
  color: var(--primary-navy);
}

/* Button Sizes */
.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-xs);
  min-height: 2rem;
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-base);
  min-height: 3.5rem;
}

/* Luxury Hero Section */
.hotel-hero {
  background: linear-gradient(135deg,
    var(--primary-dark) 0%,
    var(--primary-navy) 50%,
    var(--primary-blue) 100%);
  color: var(--white);
  position: relative;
  overflow: hidden;
  min-height: 70vh;
  display: flex;
  align-items: center;
}

.hotel-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="luxury-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23luxury-pattern)"/></svg>');
  opacity: 0.3;
}

.hotel-hero-content {
  position: relative;
  z-index: 1;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.hotel-hero h1 {
  font-family: var(--font-heading);
  font-size: var(--text-5xl);
  font-weight: 700;
  color: var(--white);
  margin-bottom: var(--space-6);
  letter-spacing: -0.025em;
  line-height: 1.1;
}

.hotel-hero p {
  font-size: var(--text-xl);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--space-12);
  line-height: 1.6;
}

/* Luxury Search Card */
.hotel-search-card {
  background: var(--white);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-2xl);
  border: 1px solid var(--gray-200);
  max-width: 900px;
  margin: 0 auto;
  position: relative;
  backdrop-filter: blur(10px);
}

.hotel-search-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-gold) 0%, var(--accent-gold-light) 100%);
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

/* Luxury Form Styles */
.hotel-form-group {
  margin-bottom: var(--space-6);
}

.hotel-form-label {
  display: block;
  margin-bottom: var(--space-2);
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--primary-dark);
  letter-spacing: 0.025em;
}

.hotel-form-input,
.hotel-form-select {
  width: 100%;
  padding: var(--space-4) var(--space-4);
  font-size: var(--text-base);
  font-family: var(--font-body);
  line-height: 1.5;
  color: var(--gray-800);
  background-color: var(--white);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
  outline: none;
}

.hotel-form-input:focus,
.hotel-form-select:focus {
  border-color: var(--accent-gold);
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
  transform: translateY(-1px);
}

/* Luxury Room Cards */
.hotel-room-card {
  background: var(--white);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-normal);
  position: relative;
}

.hotel-room-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
  border-color: var(--accent-gold);
}

.hotel-room-image {
  width: 100%;
  height: 16rem;
  background: linear-gradient(135deg, var(--gray-200) 0%, var(--gray-300) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-500);
  font-size: var(--text-lg);
  position: relative;
  overflow: hidden;
}

.hotel-room-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(212, 175, 55, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform var(--transition-slow);
}

.hotel-room-card:hover .hotel-room-image::before {
  transform: translateX(100%);
}

.hotel-room-price {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  background: linear-gradient(135deg, var(--accent-gold) 0%, var(--accent-gold-dark) 100%);
  color: var(--white);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-full);
  font-weight: 700;
  font-size: var(--text-sm);
  box-shadow: var(--shadow-gold);
  letter-spacing: 0.025em;
}

.hotel-room-content {
  padding: var(--space-6);
}

.hotel-room-title {
  font-family: var(--font-heading);
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--primary-dark);
  margin-bottom: var(--space-2);
}

.hotel-room-subtitle {
  color: var(--gray-500);
  font-size: var(--text-sm);
  margin-bottom: var(--space-4);
}

.hotel-amenities {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-bottom: var(--space-6);
}

.hotel-amenity-tag {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  background: var(--gray-100);
  color: var(--gray-700);
  font-size: var(--text-xs);
  font-weight: 500;
  border-radius: var(--radius-full);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-fast);
}

.hotel-amenity-tag:hover {
  background: var(--accent-gold);
  color: var(--white);
  border-color: var(--accent-gold);
}

/* Hotel Sections */
.hotel-section {
  padding: var(--space-20) 0;
}

.hotel-section-header {
  text-align: center;
  margin-bottom: var(--space-16);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hotel-section-title {
  font-family: var(--font-heading);
  font-size: var(--text-4xl);
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: var(--space-4);
  letter-spacing: -0.025em;
}

.hotel-section-subtitle {
  font-size: var(--text-lg);
  color: var(--gray-600);
  line-height: 1.7;
}

/* Feature Cards */
.hotel-feature-card {
  text-align: center;
  padding: var(--space-8);
  background: var(--white);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.hotel-feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--accent-gold);
}

.hotel-feature-icon {
  width: 5rem;
  height: 5rem;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-6);
  font-size: var(--text-3xl);
  background: linear-gradient(135deg, var(--accent-gold) 0%, var(--accent-gold-light) 100%);
  color: var(--white);
  box-shadow: var(--shadow-gold);
}

.hotel-feature-title {
  font-family: var(--font-heading);
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--primary-dark);
  margin-bottom: var(--space-3);
}

.hotel-feature-description {
  color: var(--gray-600);
  line-height: 1.7;
}

/* Luxury Footer */
.hotel-footer {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-navy) 100%);
  color: var(--white);
  padding: var(--space-20) 0 var(--space-8) 0;
  position: relative;
}

.hotel-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-gold) 0%, var(--accent-gold-light) 100%);
}

.hotel-footer h3 {
  color: var(--white);
  font-family: var(--font-heading);
  font-size: var(--text-xl);
  font-weight: 600;
  margin-bottom: var(--space-4);
}

.hotel-footer h4 {
  color: var(--white);
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--space-4);
}

.hotel-footer p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.7;
}

.hotel-footer a {
  color: rgba(255, 255, 255, 0.8);
  transition: all var(--transition-fast);
}

.hotel-footer a:hover {
  color: var(--accent-gold);
}

.hotel-footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: var(--space-8);
  margin-top: var(--space-8);
  text-align: center;
}

.hotel-footer-bottom p {
  color: rgba(255, 255, 255, 0.6);
  font-size: var(--text-sm);
}

/* Utility Classes */
.w-full { width: 100%; }
.h-full { height: 100%; }
.max-w-4xl { max-width: 56rem; }
.max-w-6xl { max-width: 72rem; }
.mx-auto { margin-left: auto; margin-right: auto; }

.py-20 { padding-top: var(--space-20); padding-bottom: var(--space-20); }
.py-16 { padding-top: var(--space-16); padding-bottom: var(--space-16); }
.py-12 { padding-top: var(--space-12); padding-bottom: var(--space-12); }
.py-8 { padding-top: var(--space-8); padding-bottom: var(--space-8); }
.py-6 { padding-top: var(--space-6); padding-bottom: var(--space-6); }

.mb-16 { margin-bottom: var(--space-16); }
.mb-12 { margin-bottom: var(--space-12); }
.mb-8 { margin-bottom: var(--space-8); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-3 { margin-bottom: var(--space-3); }

.space-y-2 > * + * { margin-top: var(--space-2); }
.space-y-4 > * + * { margin-top: var(--space-4); }

.bg-white { background-color: var(--white); }
.bg-gray-50 { background-color: var(--gray-50); }

.text-white { color: var(--white); }
.text-gray-500 { color: var(--gray-500); }
.text-gray-600 { color: var(--gray-600); }

.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-sm { font-size: var(--text-sm); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }

.hidden { display: none; }
.block { display: block; }

/* Loading Spinner */
.hotel-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid var(--gray-200);
  border-top: 3px solid var(--accent-gold);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design - Mobile First */

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) {
  .container {
    max-width: 540px;
    padding: 0 var(--space-6);
  }

  .hotel-hero h1 {
    font-size: var(--text-6xl);
  }

  .hotel-search-card {
    padding: var(--space-10);
  }

  .grid-cols-sm-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
  .container {
    max-width: 720px;
    padding: 0 var(--space-8);
  }

  .hotel-navbar .container {
    padding: var(--space-6) var(--space-8);
  }

  .hotel-nav {
    gap: var(--space-10);
  }

  .hotel-hero {
    min-height: 80vh;
  }

  .hotel-room-image {
    height: 18rem;
  }

  .grid-cols-md-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-md-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .hidden-md { display: none !important; }
  .block-md { display: block !important; }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
  .container {
    max-width: 960px;
    padding: 0 var(--space-10);
  }

  .hotel-room-image {
    height: 20rem;
  }

  .grid-cols-lg-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-lg-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .grid-cols-lg-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
    padding: 0 var(--space-12);
  }

  .hotel-section-title {
    font-size: var(--text-5xl);
  }

  .grid-cols-xl-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-xl-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .grid-cols-xl-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/* Accessibility & Performance */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .hotel-spinner {
    animation: none;
  }
}

/* Print styles */
@media print {
  .hotel-navbar,
  .hotel-footer,
  .btn {
    display: none !important;
  }

  .hotel-room-card {
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }
}

/* Shadows */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* Overflow */
.overflow-hidden { overflow: hidden; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }

/* Display */
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.hidden { display: none; }

/* Opacity */
.opacity-0 { opacity: 0; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-90 { opacity: 0.9; }
.opacity-100 { opacity: 1; }

/* Button Components */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-size: var(--text-sm);
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  white-space: nowrap;
  user-select: none;
  outline: none;
  min-height: 2.75rem;
}

.btn:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Button Variants */
.btn-primary {
  background-color: var(--primary-600);
  color: white;
  border-color: var(--primary-600);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-700);
  border-color: var(--primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background-color: var(--gray-600);
  color: white;
  border-color: var(--gray-600);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--gray-700);
  border-color: var(--gray-700);
  transform: translateY(-1px);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-600);
  border-color: var(--primary-600);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--primary-600);
  color: white;
  transform: translateY(-1px);
}

.btn-ghost {
  background-color: transparent;
  color: var(--gray-600);
  border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
  background-color: var(--gray-100);
  color: var(--gray-800);
}

.btn-success {
  background-color: var(--success);
  color: white;
  border-color: var(--success);
}

.btn-success:hover:not(:disabled) {
  background-color: #059669;
  border-color: #059669;
  transform: translateY(-1px);
}

.btn-warning {
  background-color: var(--warning);
  color: white;
  border-color: var(--warning);
}

.btn-warning:hover:not(:disabled) {
  background-color: #d97706;
  border-color: #d97706;
  transform: translateY(-1px);
}

.btn-error {
  background-color: var(--error);
  color: white;
  border-color: var(--error);
}

.btn-error:hover:not(:disabled) {
  background-color: #dc2626;
  border-color: #dc2626;
  transform: translateY(-1px);
}

/* Button Sizes */
.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-xs);
  min-height: 2rem;
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-base);
  min-height: 3.5rem;
}

.btn-xl {
  padding: var(--space-5) var(--space-10);
  font-size: var(--text-lg);
  min-height: 4rem;
}

/* Card Components */
.card {
  background-color: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-100);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  border-color: var(--gray-200);
}

.card-body {
  padding: var(--space-6);
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-100);
  background-color: var(--gray-50);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--gray-100);
  background-color: var(--gray-50);
}

/* Form Components */
.form-group {
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  margin-bottom: var(--space-2);
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--gray-700);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-base);
  line-height: 1.5;
  color: var(--gray-900);
  background-color: white;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  outline: none;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:disabled,
.form-select:disabled,
.form-textarea:disabled {
  background-color: var(--gray-100);
  color: var(--gray-500);
  cursor: not-allowed;
}

.form-textarea {
  resize: vertical;
  min-height: 6rem;
}

.form-error {
  margin-top: var(--space-1);
  font-size: var(--text-sm);
  color: var(--error);
}

.form-help {
  margin-top: var(--space-1);
  font-size: var(--text-sm);
  color: var(--gray-500);
}

/* Search Input */
.search-input {
  position: relative;
}

.search-input input {
  padding-left: 2.5rem;
}

.search-input .search-icon {
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
  pointer-events: none;
}

/* Badge Component */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
  font-weight: 500;
  border-radius: var(--radius-full);
  white-space: nowrap;
}

.badge-primary {
  background-color: var(--primary-100);
  color: var(--primary-800);
}

.badge-success {
  background-color: #d1fae5;
  color: #065f46;
}

.badge-warning {
  background-color: #fef3c7;
  color: #92400e;
}

.badge-error {
  background-color: #fee2e2;
  color: #991b1b;
}

.badge-gray {
  background-color: var(--gray-100);
  color: var(--gray-800);
}

/* Avatar Component */
.avatar {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  background-color: var(--gray-200);
  color: var(--gray-600);
  font-weight: 500;
  overflow: hidden;
}

.avatar-sm {
  width: 2rem;
  height: 2rem;
  font-size: var(--text-xs);
}

.avatar-md {
  width: 2.5rem;
  height: 2.5rem;
  font-size: var(--text-sm);
}

.avatar-lg {
  width: 3rem;
  height: 3rem;
  font-size: var(--text-base);
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Loading Spinner */
.spinner {
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid var(--gray-200);
  border-top: 2px solid var(--primary-600);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

.spinner-sm {
  width: 1rem;
  height: 1rem;
  border-width: 1px;
}

.spinner-lg {
  width: 2rem;
  height: 2rem;
  border-width: 3px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Navigation Components */
.navbar {
  background-color: white;
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 40;
}

.navbar-brand {
  font-family: var(--font-secondary);
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--primary-600);
  text-decoration: none;
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: var(--space-6);
}

.navbar-link {
  color: var(--gray-600);
  font-weight: 500;
  text-decoration: none;
  transition: color var(--transition-fast);
}

.navbar-link:hover {
  color: var(--primary-600);
}

.navbar-link.active {
  color: var(--primary-600);
  font-weight: 600;
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.1;
}

.hero-content {
  position: relative;
  z-index: 1;
}

/* Search Card */
.search-card {
  background: white;
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--gray-100);
}

/* Feature Cards */
.feature-card {
  text-align: center;
  padding: var(--space-8);
}

.feature-icon {
  width: 4rem;
  height: 4rem;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-4);
  font-size: var(--text-2xl);
}

/* Room Cards */
.room-card {
  background: white;
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-100);
}

.room-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.room-image {
  width: 100%;
  height: 12rem;
  background: linear-gradient(135deg, var(--gray-200) 0%, var(--gray-300) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-500);
  font-size: var(--text-lg);
  position: relative;
  overflow: hidden;
}

.room-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.room-price {
  position: absolute;
  top: var(--space-3);
  right: var(--space-3);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-full);
  font-weight: 600;
  color: var(--primary-600);
  font-size: var(--text-sm);
}

.amenity-tag {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  background-color: var(--gray-100);
  color: var(--gray-700);
  font-size: var(--text-xs);
  font-weight: 500;
  border-radius: var(--radius-full);
  white-space: nowrap;
}

/* Footer */
.footer {
  background-color: var(--gray-900);
  color: var(--gray-300);
}

.footer h4 {
  color: white;
  margin-bottom: var(--space-4);
}

.footer a {
  color: var(--gray-400);
  transition: color var(--transition-fast);
}

.footer a:hover {
  color: white;
}

/* Responsive Design - Mobile First Approach */

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) {
  .container {
    max-width: 540px;
    padding: 0 var(--space-6);
  }

  .grid-cols-sm-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }

  h1 { font-size: var(--text-4xl); }
  h2 { font-size: var(--text-3xl); }

  .search-card {
    padding: var(--space-10);
  }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
  .container {
    max-width: 720px;
    padding: 0 var(--space-8);
  }

  .grid-cols-md-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .grid-cols-md-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .grid-cols-md-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

  h1 { font-size: var(--text-5xl); }
  h2 { font-size: var(--text-4xl); }
  h3 { font-size: var(--text-2xl); }

  .navbar-nav {
    gap: var(--space-8);
  }

  .room-image {
    height: 14rem;
  }

  .feature-card {
    padding: var(--space-10);
  }

  .search-card {
    padding: var(--space-12);
  }

  /* Show/hide utilities for tablets */
  .hidden-md { display: none !important; }
  .block-md { display: block !important; }
  .flex-md { display: flex !important; }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
  .container {
    max-width: 960px;
    padding: 0 var(--space-10);
  }

  .grid-cols-lg-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .grid-cols-lg-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .grid-cols-lg-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .grid-cols-lg-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }

  .room-image {
    height: 16rem;
  }

  /* Show/hide utilities for desktops */
  .hidden-lg { display: none !important; }
  .block-lg { display: block !important; }
  .flex-lg { display: flex !important; }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
    padding: 0 var(--space-12);
  }

  .grid-cols-xl-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .grid-cols-xl-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .grid-cols-xl-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .grid-cols-xl-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
  .grid-cols-xl-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }

  .room-image {
    height: 18rem;
  }

  /* Show/hide utilities for large desktops */
  .hidden-xl { display: none !important; }
  .block-xl { display: block !important; }
  .flex-xl { display: flex !important; }
}

/* Utility Classes for Responsive Design */
.mobile-only {
  display: block;
}

.desktop-only {
  display: none;
}

@media (min-width: 768px) {
  .mobile-only {
    display: none;
  }

  .desktop-only {
    display: block;
  }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
  :root {
    --gray-50: #1f2937;
    --gray-100: #374151;
    --gray-200: #4b5563;
    --gray-300: #6b7280;
    --gray-800: #f9fafb;
    --gray-900: #ffffff;
  }

  body {
    background-color: var(--gray-50);
    color: var(--gray-800);
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .btn {
    border: 1px solid #000 !important;
    background: transparent !important;
    color: #000 !important;
  }

  .card {
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }
}

/* Focus styles for accessibility */
.btn:focus-visible,
.form-input:focus-visible,
.form-select:focus-visible,
.form-textarea:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .spinner {
    animation: none;
  }
}

/* Additional Utility Classes */
.space-y-2 > * + * { margin-top: var(--space-2); }
.space-y-3 > * + * { margin-top: var(--space-3); }
.space-y-4 > * + * { margin-top: var(--space-4); }

.border { border: 1px solid var(--gray-200); }
.border-t { border-top: 1px solid var(--gray-200); }
.border-b { border-bottom: 1px solid var(--gray-200); }
.border-gray-700 { border-color: var(--gray-700); }

.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

.select-none { user-select: none; }

.pointer-events-none { pointer-events: none; }

/* Icon utilities */
.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
}

.icon-sm {
  width: 1rem;
  height: 1rem;
}

.icon-lg {
  width: 1.5rem;
  height: 1.5rem;
}

/* Hover effects */
.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Focus ring */
.focus-ring:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* Backdrop blur */
.backdrop-blur {
  backdrop-filter: blur(8px);
}

/* Gradient backgrounds */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
}

.bg-gradient-gray {
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
}
