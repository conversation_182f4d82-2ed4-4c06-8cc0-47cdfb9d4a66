// App Constants for Ghana Guesthouse Management System

// Currency Configuration
export const CURRENCY = {
  CODE: 'GHS',
  SYMBOL: '₵',
  NAME: 'Ghana Cedis'
};

// Room Types
export const ROOM_TYPES = {
  SINGLE: 'single',
  DOUBLE: 'double',
  SUITE: 'suite'
};

// Room Amenities
export const AMENITIES = {
  AC: 'Air Conditioning',
  WIFI: 'WiFi',
  TV: 'Television',
  FRIDGE: 'Refrigerator',
  BALCONY: 'Balcony',
  BATHROOM: 'Private Bathroom',
  BREAKFAST: 'Breakfast Included',
  PARKING: 'Free Parking'
};

// Booking Status
export const BOOKING_STATUS = {
  CONFIRMED: 'confirmed',
  CANCELLED: 'cancelled',
  COMPLETED: 'completed',
  PENDING: 'pending'
};

// Payment Status
export const PAYMENT_STATUS = {
  PENDING: 'pending',
  PAID: 'paid',
  FAILED: 'failed',
  REFUNDED: 'refunded'
};

// Payment Methods
export const PAYMENT_METHODS = {
  MOBILE_MONEY: 'mobile_money',
  CARD: 'card',
  CASH: 'cash',
  BANK_TRANSFER: 'bank_transfer'
};

// User Roles
export const USER_ROLES = {
  GUEST: 'guest',
  ADMIN: 'admin'
};

// Ghana Mobile Money Providers
export const MOBILE_MONEY_PROVIDERS = {
  MTN: 'MTN Mobile Money',
  VODAFONE: 'Vodafone Cash',
  AIRTELTIGO: 'AirtelTigo Money'
};

// Room Configuration
export const ROOM_CONFIG = {
  TOTAL_ROOMS: 10,
  MIN_CAPACITY: 1,
  MAX_CAPACITY: 4,
  ROOM_NUMBERS: Array.from({ length: 10 }, (_, i) => `R${String(i + 1).padStart(3, '0')}`)
};

// Date Formats
export const DATE_FORMATS = {
  DISPLAY: 'dd/MM/yyyy',
  API: 'yyyy-MM-dd',
  DATETIME: 'dd/MM/yyyy HH:mm'
};

// API Endpoints (will be configured with Supabase)
export const API_ENDPOINTS = {
  ROOMS: '/rooms',
  BOOKINGS: '/bookings',
  PAYMENTS: '/payments',
  USERS: '/users',
  AUTH: '/auth'
};

// Form Validation Rules
export const VALIDATION_RULES = {
  PHONE: {
    GHANA_PATTERN: /^(\+233|0)[2-9][0-9]{8}$/,
    MESSAGE: 'Please enter a valid Ghana phone number'
  },
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    MESSAGE: 'Please enter a valid email address'
  },
  PASSWORD: {
    MIN_LENGTH: 8,
    MESSAGE: 'Password must be at least 8 characters long'
  }
};

// App Routes
export const ROUTES = {
  HOME: '/',
  ROOMS: '/rooms',
  ROOM_DETAILS: '/rooms/:id',
  BOOKING: '/booking',
  LOGIN: '/login',
  REGISTER: '/register',
  PROFILE: '/profile',
  ADMIN: '/admin',
  ADMIN_DASHBOARD: '/admin/dashboard',
  ADMIN_ROOMS: '/admin/rooms',
  ADMIN_BOOKINGS: '/admin/bookings',
  ADMIN_GUESTS: '/admin/guests',
  ADMIN_PAYMENTS: '/admin/payments',
  ADMIN_REPORTS: '/admin/reports'
};

// Local Storage Keys
export const STORAGE_KEYS = {
  USER: 'guesthouse_user',
  TOKEN: 'guesthouse_token',
  BOOKING_DRAFT: 'booking_draft',
  PREFERENCES: 'user_preferences'
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  VALIDATION: 'Please check your input and try again.',
  BOOKING_CONFLICT: 'Room is not available for selected dates.',
  PAYMENT_FAILED: 'Payment processing failed. Please try again.'
};

// Success Messages
export const SUCCESS_MESSAGES = {
  BOOKING_CREATED: 'Booking created successfully!',
  PAYMENT_SUCCESS: 'Payment completed successfully!',
  PROFILE_UPDATED: 'Profile updated successfully!',
  ROOM_UPDATED: 'Room information updated successfully!'
};
