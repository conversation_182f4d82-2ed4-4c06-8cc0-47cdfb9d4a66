// Room search component with filters

import { useState, useEffect } from 'react';
import { formatDate, validateDateRange, getMinCheckInDate, getMinCheckOutDate } from '../../utils/date';
import { formatCurrency } from '../../utils/currency';
import { roomService } from '../../services/roomService';
import toast from 'react-hot-toast';

const RoomSearch = ({ onSearchResults, onFiltersChange }) => {
  const [filters, setFilters] = useState({
    checkIn: '',
    checkOut: '',
    guests: 1,
    roomType: 'all',
    minPrice: '',
    maxPrice: '',
    amenities: []
  });

  const [loading, setLoading] = useState(false);
  const [roomTypes, setRoomTypes] = useState([]);
  const [priceRange, setPriceRange] = useState({ minPrice: 0, maxPrice: 1000 });
  const [availableAmenities, setAvailableAmenities] = useState([]);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // Load filter options on component mount
  useEffect(() => {
    loadFilterOptions();
  }, []);

  const loadFilterOptions = async () => {
    try {
      const [typesResult, priceResult, amenitiesResult] = await Promise.all([
        roomService.getRoomTypes(),
        roomService.getPriceRange(),
        roomService.getAvailableAmenities()
      ]);

      if (typesResult.data) setRoomTypes(typesResult.data);
      if (priceResult.data) setPriceRange(priceResult.data);
      if (amenitiesResult.data) setAvailableAmenities(amenitiesResult.data);
    } catch (error) {
      console.error('Error loading filter options:', error);
    }
  };

  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    
    // Notify parent component of filter changes
    if (onFiltersChange) {
      onFiltersChange(newFilters);
    }
  };

  const handleAmenityToggle = (amenity) => {
    const newAmenities = filters.amenities.includes(amenity)
      ? filters.amenities.filter(a => a !== amenity)
      : [...filters.amenities, amenity];
    
    handleFilterChange('amenities', newAmenities);
  };

  const handleSearch = async () => {
    // Validate date range if dates are provided
    if (filters.checkIn && filters.checkOut) {
      const validation = validateDateRange(filters.checkIn, filters.checkOut);
      if (!validation.isValid) {
        toast.error(validation.errors[0]);
        return;
      }
    }

    setLoading(true);

    try {
      let searchResults;

      if (filters.checkIn && filters.checkOut) {
        // Search with date availability
        searchResults = await roomService.getAvailableRooms(
          filters.checkIn,
          filters.checkOut,
          {
            roomType: filters.roomType,
            minPrice: filters.minPrice ? parseFloat(filters.minPrice) : null,
            maxPrice: filters.maxPrice ? parseFloat(filters.maxPrice) : null,
            capacity: filters.guests,
            amenities: filters.amenities
          }
        );
      } else {
        // Search without date constraints
        searchResults = await roomService.searchRooms({
          roomType: filters.roomType,
          minPrice: filters.minPrice ? parseFloat(filters.minPrice) : null,
          maxPrice: filters.maxPrice ? parseFloat(filters.maxPrice) : null,
          capacity: filters.guests,
          amenities: filters.amenities
        });
      }

      if (searchResults.error) {
        toast.error('Error searching rooms. Please try again.');
        return;
      }

      // Pass results to parent component
      if (onSearchResults) {
        onSearchResults(searchResults.data, filters);
      }

      toast.success(`Found ${searchResults.data.length} available rooms`);
    } catch (error) {
      console.error('Search error:', error);
      toast.error('Search failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const clearFilters = () => {
    const clearedFilters = {
      checkIn: '',
      checkOut: '',
      guests: 1,
      roomType: 'all',
      minPrice: '',
      maxPrice: '',
      amenities: []
    };
    setFilters(clearedFilters);
    if (onFiltersChange) {
      onFiltersChange(clearedFilters);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <h3 className="text-xl font-semibold mb-4">Search Rooms</h3>
      
      {/* Basic Search */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
        <div className="form-group">
          <label className="form-label">Check-in Date</label>
          <input
            type="date"
            value={filters.checkIn}
            onChange={(e) => handleFilterChange('checkIn', e.target.value)}
            className="form-input"
            min={formatDate(getMinCheckInDate(), 'yyyy-MM-dd')}
          />
        </div>

        <div className="form-group">
          <label className="form-label">Check-out Date</label>
          <input
            type="date"
            value={filters.checkOut}
            onChange={(e) => handleFilterChange('checkOut', e.target.value)}
            className="form-input"
            min={filters.checkIn || formatDate(getMinCheckOutDate(), 'yyyy-MM-dd')}
          />
        </div>

        <div className="form-group">
          <label className="form-label">Guests</label>
          <select
            value={filters.guests}
            onChange={(e) => handleFilterChange('guests', parseInt(e.target.value))}
            className="form-input"
          >
            <option value={1}>1 Guest</option>
            <option value={2}>2 Guests</option>
            <option value={3}>3 Guests</option>
            <option value={4}>4 Guests</option>
          </select>
        </div>

        <div className="form-group">
          <label className="form-label">Room Type</label>
          <select
            value={filters.roomType}
            onChange={(e) => handleFilterChange('roomType', e.target.value)}
            className="form-input"
          >
            <option value="all">All Types</option>
            {roomTypes.map(type => (
              <option key={type} value={type}>
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Advanced Filters Toggle */}
      <div className="mb-4">
        <button
          type="button"
          onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
          className="text-primary hover:text-accent font-medium"
        >
          {showAdvancedFilters ? '− Hide' : '+ Show'} Advanced Filters
        </button>
      </div>

      {/* Advanced Filters */}
      {showAdvancedFilters && (
        <div className="border-t pt-4 mb-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div className="form-group">
              <label className="form-label">Min Price ({formatCurrency(0).split('0')[0]})</label>
              <input
                type="number"
                value={filters.minPrice}
                onChange={(e) => handleFilterChange('minPrice', e.target.value)}
                className="form-input"
                placeholder={`Min ${priceRange.minPrice}`}
                min={priceRange.minPrice}
                max={priceRange.maxPrice}
              />
            </div>

            <div className="form-group">
              <label className="form-label">Max Price ({formatCurrency(0).split('0')[0]})</label>
              <input
                type="number"
                value={filters.maxPrice}
                onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
                className="form-input"
                placeholder={`Max ${priceRange.maxPrice}`}
                min={priceRange.minPrice}
                max={priceRange.maxPrice}
              />
            </div>
          </div>

          {/* Amenities Filter */}
          <div className="form-group">
            <label className="form-label">Amenities</label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {availableAmenities.map(amenity => (
                <label key={amenity} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.amenities.includes(amenity)}
                    onChange={() => handleAmenityToggle(amenity)}
                    className="mr-2"
                  />
                  <span className="text-sm">{amenity}</span>
                </label>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Search Actions */}
      <div className="flex flex-col sm:flex-row gap-3">
        <button
          onClick={handleSearch}
          disabled={loading}
          className="btn btn-primary flex-1"
        >
          {loading ? (
            <div className="flex items-center justify-center">
              <div className="spinner mr-2"></div>
              Searching...
            </div>
          ) : (
            'Search Rooms'
          )}
        </button>

        <button
          onClick={clearFilters}
          className="btn btn-outline"
        >
          Clear Filters
        </button>
      </div>
    </div>
  );
};

export default RoomSearch;
