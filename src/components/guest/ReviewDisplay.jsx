// Component to display reviews and ratings

import { useState, useEffect } from 'react';
import { reviewService } from '../../services/reviewService';
import { formatDate } from '../../utils/date';

const ReviewDisplay = ({ roomId, showAll = false }) => {
  const [reviews, setReviews] = useState([]);
  const [ratingSummary, setRatingSummary] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showAllReviews, setShowAllReviews] = useState(showAll);

  useEffect(() => {
    loadReviews();
    loadRatingSummary();
  }, [roomId]);

  const loadReviews = async () => {
    try {
      const result = await reviewService.getRoomReviews(roomId, showAllReviews ? 50 : 6);
      if (result.data) {
        setReviews(result.data);
      }
    } catch (error) {
      console.error('Error loading reviews:', error);
    }
  };

  const loadRatingSummary = async () => {
    try {
      const result = await reviewService.getRoomRatingSummary(roomId);
      if (result.data) {
        setRatingSummary(result.data);
      }
    } catch (error) {
      console.error('Error loading rating summary:', error);
    } finally {
      setLoading(false);
    }
  };

  const StarDisplay = ({ rating, size = 'text-sm' }) => {
    return (
      <div className={`flex items-center ${size}`}>
        {[1, 2, 3, 4, 5].map((star) => (
          <span
            key={star}
            className={star <= rating ? 'text-yellow-500' : 'text-gray-300'}
          >
            ⭐
          </span>
        ))}
        <span className="ml-1 text-gray-600">({rating.toFixed(1)})</span>
      </div>
    );
  };

  const RatingBar = ({ label, rating, count, total }) => {
    const percentage = total > 0 ? (count / total) * 100 : 0;
    
    return (
      <div className="flex items-center space-x-3">
        <span className="w-16 text-sm">{label}</span>
        <div className="flex-1 bg-gray-200 rounded-full h-2">
          <div 
            className="bg-yellow-500 h-2 rounded-full" 
            style={{ width: `${percentage}%` }}
          ></div>
        </div>
        <span className="w-8 text-sm text-gray-600">{count}</span>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center">
          <div className="spinner mx-auto mb-4"></div>
          <p className="text-gray-600">Loading reviews...</p>
        </div>
      </div>
    );
  }

  if (!ratingSummary || ratingSummary.totalReviews === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-xl font-semibold mb-4">Reviews</h3>
        <div className="text-center py-8">
          <div className="text-4xl mb-2">⭐</div>
          <p className="text-gray-600">No reviews yet</p>
          <p className="text-sm text-gray-500">Be the first to leave a review!</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-xl font-semibold mb-6">Reviews & Ratings</h3>

      {/* Rating Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {/* Overall Rating */}
        <div className="text-center">
          <div className="text-4xl font-bold text-primary mb-2">
            {ratingSummary.averageRating.toFixed(1)}
          </div>
          <StarDisplay rating={ratingSummary.averageRating} size="text-lg" />
          <p className="text-sm text-gray-600 mt-1">
            Based on {ratingSummary.totalReviews} review{ratingSummary.totalReviews !== 1 ? 's' : ''}
          </p>
          <p className="text-sm text-green-600 mt-2">
            {ratingSummary.recommendationRate.toFixed(0)}% would recommend
          </p>
        </div>

        {/* Rating Breakdown */}
        <div className="space-y-2">
          {Object.entries(ratingSummary.ratingBreakdown)
            .sort(([a], [b]) => b - a)
            .map(([rating, count]) => (
              <RatingBar
                key={rating}
                label={`${rating} star${rating !== '1' ? 's' : ''}`}
                rating={parseInt(rating)}
                count={count}
                total={ratingSummary.totalReviews}
              />
            ))}
        </div>
      </div>

      {/* Category Ratings */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8 p-4 bg-gray-50 rounded-lg">
        {Object.entries(ratingSummary.categoryAverages).map(([category, average]) => (
          <div key={category} className="text-center">
            <div className="text-lg font-semibold">{average.toFixed(1)}</div>
            <div className="text-xs text-gray-600 capitalize">{category}</div>
          </div>
        ))}
      </div>

      {/* Individual Reviews */}
      {reviews.length > 0 && (
        <div>
          <div className="flex justify-between items-center mb-4">
            <h4 className="text-lg font-semibold">Guest Reviews</h4>
            {!showAll && reviews.length >= 6 && (
              <button
                onClick={() => {
                  setShowAllReviews(true);
                  loadReviews();
                }}
                className="text-primary hover:text-accent text-sm"
              >
                View All Reviews →
              </button>
            )}
          </div>

          <div className="space-y-6">
            {reviews.map((review) => (
              <div key={review.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <div className="font-semibold">
                      {review.guest?.full_name || 'Anonymous Guest'}
                    </div>
                    <div className="text-sm text-gray-600">
                      {formatDate(review.created_at)}
                    </div>
                  </div>
                  <StarDisplay rating={review.overall_rating} />
                </div>

                {review.review_text && (
                  <p className="text-gray-700 mb-3">{review.review_text}</p>
                )}

                {/* Category Ratings */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-gray-600">
                  <div>Cleanliness: {review.cleanliness_rating}/5</div>
                  <div>Comfort: {review.comfort_rating}/5</div>
                  <div>Service: {review.service_rating}/5</div>
                  <div>Value: {review.value_rating}/5</div>
                </div>

                {review.would_recommend && (
                  <div className="mt-2">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                      ✓ Recommends this place
                    </span>
                  </div>
                )}
              </div>
            ))}
          </div>

          {showAllReviews && reviews.length >= 50 && (
            <div className="text-center mt-6">
              <p className="text-sm text-gray-600">
                Showing latest 50 reviews
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ReviewDisplay;
