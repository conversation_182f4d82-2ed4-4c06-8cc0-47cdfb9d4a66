// Review form component for guests to leave reviews

import { useState } from 'react';
import { reviewService } from '../../services/reviewService';
import { useAuth } from '../../context/AuthContext';
import toast from 'react-hot-toast';

const ReviewForm = ({ booking, onReviewSubmitted, onCancel }) => {
  const { user } = useAuth();
  const [submitting, setSubmitting] = useState(false);
  const [reviewData, setReviewData] = useState({
    overallRating: 5,
    cleanlinessRating: 5,
    comfortRating: 5,
    serviceRating: 5,
    valueRating: 5,
    reviewText: '',
    wouldRecommend: true
  });

  const handleRatingChange = (category, rating) => {
    setReviewData(prev => ({ ...prev, [category]: rating }));
  };

  const handleTextChange = (e) => {
    setReviewData(prev => ({ ...prev, reviewText: e.target.value }));
  };

  const handleRecommendChange = (e) => {
    setReviewData(prev => ({ ...prev, wouldRecommend: e.target.checked }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!reviewData.reviewText.trim()) {
      toast.error('Please write a review comment');
      return;
    }

    setSubmitting(true);

    try {
      const result = await reviewService.createReview({
        bookingId: booking.id,
        guestId: user.id,
        roomId: booking.room_id,
        ...reviewData
      });

      if (result.error) {
        toast.error('Failed to submit review');
        return;
      }

      toast.success('Review submitted successfully!');
      if (onReviewSubmitted) {
        onReviewSubmitted(result.data);
      }
    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error('Failed to submit review');
    } finally {
      setSubmitting(false);
    }
  };

  const StarRating = ({ rating, onRatingChange, label }) => {
    return (
      <div className="form-group">
        <label className="form-label">{label}</label>
        <div className="flex items-center space-x-1">
          {[1, 2, 3, 4, 5].map((star) => (
            <button
              key={star}
              type="button"
              onClick={() => onRatingChange(star)}
              className={`text-2xl ${
                star <= rating ? 'text-yellow-500' : 'text-gray-300'
              } hover:text-yellow-500 transition-colors`}
            >
              ⭐
            </button>
          ))}
          <span className="ml-2 text-sm text-gray-600">({rating}/5)</span>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-xl font-semibold mb-4">Leave a Review</h3>
      
      <div className="mb-4 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-semibold">Your Stay</h4>
        <p className="text-sm text-gray-600">
          {booking.room?.room_type?.charAt(0).toUpperCase() + booking.room?.room_type?.slice(1)} Room {booking.room?.room_number}
        </p>
        <p className="text-sm text-gray-600">
          {new Date(booking.check_in_date).toLocaleDateString()} - {new Date(booking.check_out_date).toLocaleDateString()}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Overall Rating */}
        <StarRating
          rating={reviewData.overallRating}
          onRatingChange={(rating) => handleRatingChange('overallRating', rating)}
          label="Overall Rating *"
        />

        {/* Category Ratings */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <StarRating
            rating={reviewData.cleanlinessRating}
            onRatingChange={(rating) => handleRatingChange('cleanlinessRating', rating)}
            label="Cleanliness"
          />
          
          <StarRating
            rating={reviewData.comfortRating}
            onRatingChange={(rating) => handleRatingChange('comfortRating', rating)}
            label="Comfort"
          />
          
          <StarRating
            rating={reviewData.serviceRating}
            onRatingChange={(rating) => handleRatingChange('serviceRating', rating)}
            label="Service"
          />
          
          <StarRating
            rating={reviewData.valueRating}
            onRatingChange={(rating) => handleRatingChange('valueRating', rating)}
            label="Value for Money"
          />
        </div>

        {/* Review Text */}
        <div className="form-group">
          <label className="form-label">Your Review *</label>
          <textarea
            value={reviewData.reviewText}
            onChange={handleTextChange}
            className="form-input"
            rows="4"
            placeholder="Tell us about your experience..."
            required
          />
          <p className="text-xs text-gray-500 mt-1">
            Share details about your stay to help other guests
          </p>
        </div>

        {/* Recommendation */}
        <div className="form-group">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={reviewData.wouldRecommend}
              onChange={handleRecommendChange}
              className="mr-2"
            />
            <span>I would recommend this place to others</span>
          </label>
        </div>

        {/* Submit Buttons */}
        <div className="flex gap-3">
          <button
            type="submit"
            disabled={submitting}
            className="btn btn-primary flex-1"
          >
            {submitting ? (
              <div className="flex items-center justify-center">
                <div className="spinner mr-2"></div>
                Submitting...
              </div>
            ) : (
              'Submit Review'
            )}
          </button>
          
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="btn btn-outline flex-1"
            >
              Cancel
            </button>
          )}
        </div>
      </form>
    </div>
  );
};

export default ReviewForm;
