import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { formatCurrency } from '../utils/currency';
import { roomService } from '../services/roomService';
import { formatDate } from '../utils/date';

const HomePage = () => {
  const { user, isAuthenticated, isAdmin } = useAuth();
  const [checkIn, setCheckIn] = useState('');
  const [checkOut, setCheckOut] = useState('');
  const [featuredRooms, setFeaturedRooms] = useState([]);
  const [loading, setLoading] = useState(true);

  // Load featured rooms from Supabase
  useEffect(() => {
    loadFeaturedRooms();
  }, []);

  const loadFeaturedRooms = async () => {
    try {
      const result = await roomService.getAllRooms();
      if (result.data) {
        // Show first 3 rooms as featured
        setFeaturedRooms(result.data.slice(0, 3));
      }
    } catch (error) {
      console.error('Error loading featured rooms:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-md">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-primary">
              🏨 Ghana Guesthouse
            </h1>
            <nav className="flex items-center space-x-4">
              {isAuthenticated() ? (
                <>
                  <span className="text-gray-600">
                    Welcome, {user?.email}
                  </span>
                  <a href="/dashboard" className="btn btn-outline">
                    My Dashboard
                  </a>
                  {isAdmin() && (
                    <a href="/admin" className="btn btn-secondary">
                      Admin Panel
                    </a>
                  )}
                  <button className="btn btn-outline">
                    Sign Out
                  </button>
                </>
              ) : (
                <>
                  <a href="/login" className="btn btn-outline">
                    Sign In
                  </a>
                  <a href="/rooms" className="btn btn-secondary">
                    View Rooms
                  </a>
                </>
              )}
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="bg-primary text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-4">
            Welcome to Ghana's Premier Guesthouse
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Experience comfort and hospitality in the heart of Ghana
          </p>

          {/* Quick Search */}
          <div className="bg-white rounded-lg p-6 max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
              <div className="text-left">
                <label className="form-label text-gray-700">Check-in Date</label>
                <input
                  type="date"
                  value={checkIn}
                  onChange={(e) => setCheckIn(e.target.value)}
                  className="form-input"
                  min={formatDate(new Date(), 'yyyy-MM-dd')}
                />
              </div>
              <div className="text-left">
                <label className="form-label text-gray-700">Check-out Date</label>
                <input
                  type="date"
                  value={checkOut}
                  onChange={(e) => setCheckOut(e.target.value)}
                  className="form-input"
                  min={checkIn || formatDate(new Date(), 'yyyy-MM-dd')}
                />
              </div>
              <div className="text-left">
                <label className="form-label text-gray-700">Guests</label>
                <select className="form-input">
                  <option value="1">1 Guest</option>
                  <option value="2">2 Guests</option>
                  <option value="3">3 Guests</option>
                  <option value="4">4 Guests</option>
                </select>
              </div>
              <button
                className="btn btn-primary h-10"
                onClick={() => {
                  if (checkIn && checkOut) {
                    window.location.href = `/rooms?checkIn=${checkIn}&checkOut=${checkOut}&guests=1`;
                  } else {
                    window.location.href = '/rooms';
                  }
                }}
              >
                Search Rooms
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Rooms */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h3 className="text-3xl font-bold text-center mb-12">
            Our Featured Rooms
          </h3>

          {loading ? (
            <div className="text-center py-12">
              <div className="spinner mx-auto mb-4"></div>
              <p className="text-gray-600">Loading featured rooms...</p>
            </div>
          ) : featuredRooms.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">🏨</div>
              <h4 className="text-xl font-semibold mb-2">No rooms available</h4>
              <p className="text-gray-600 mb-4">
                Please check back later or contact us directly.
              </p>
              <a href="/rooms" className="btn btn-primary">
                View All Rooms
              </a>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredRooms.map((room) => (
              <div key={room.id} className="card">
                <div className="h-48 bg-gray-200 rounded-t-lg flex items-center justify-center">
                  <span className="text-gray-500">Room Image</span>
                </div>
                <div className="card-body">
                  <h4 className="text-xl font-semibold mb-2">
                    {room.room_type.charAt(0).toUpperCase() + room.room_type.slice(1)} Room
                  </h4>
                  <p className="text-gray-600 mb-4">
                    Room {room.room_number} • Up to {room.capacity} guest{room.capacity > 1 ? 's' : ''}
                  </p>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {room.amenities.map((amenity) => (
                      <span
                        key={amenity}
                        className="px-2 py-1 bg-gray-100 text-gray-700 text-sm rounded"
                      >
                        {amenity}
                      </span>
                    ))}
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-2xl font-bold text-primary">
                      {formatCurrency(room.price_per_night)}/night
                    </span>
                    <a href={`/rooms/${room.id}`} className="btn btn-primary">
                      View Details
                    </a>
                  </div>
                </div>
              </div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Features */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-4">
          <h3 className="text-3xl font-bold text-center mb-12">
            Why Choose Us?
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl">🏨</span>
              </div>
              <h4 className="text-xl font-semibold mb-2">Prime Location</h4>
              <p className="text-gray-600">
                Located in the heart of Ghana with easy access to major attractions
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-secondary rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-dark text-2xl">💳</span>
              </div>
              <h4 className="text-xl font-semibold mb-2">Easy Payment</h4>
              <p className="text-gray-600">
                Multiple payment options including Mobile Money and cards
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl">⭐</span>
              </div>
              <h4 className="text-xl font-semibold mb-2">Quality Service</h4>
              <p className="text-gray-600">
                24/7 customer service and premium amenities for your comfort
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-dark text-white py-8">
        <div className="container mx-auto px-4 text-center">
          <p>&copy; 2024 Ghana Guesthouse Management System. All rights reserved.</p>
          <p className="text-gray-400 mt-2">
            Built with ❤️ for Ghana's hospitality industry
          </p>
        </div>
      </footer>
    </div>
  );
};

export default HomePage;