import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { formatCurrency } from '../utils/currency';
import { roomService } from '../services/roomService';
import { formatDate } from '../utils/date';

const HomePage = () => {
  const { user, isAuthenticated, isAdmin } = useAuth();
  const [checkIn, setCheckIn] = useState('');
  const [checkOut, setCheckOut] = useState('');
  const [featuredRooms, setFeaturedRooms] = useState([]);
  const [loading, setLoading] = useState(true);

  // Load featured rooms from Supabase
  useEffect(() => {
    loadFeaturedRooms();
  }, []);

  const loadFeaturedRooms = async () => {
    try {
      const result = await roomService.getAllRooms();
      if (result.data) {
        // Show first 3 rooms as featured
        setFeaturedRooms(result.data.slice(0, 3));
      }
    } catch (error) {
      console.error('Error loading featured rooms:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      {/* Header */}
      <header className="navbar">
        <div className="container">
          <div>
            <a href="/" className="navbar-brand">
              🏨 Ghana Guesthouse
            </a>
            <nav className="navbar-nav">
              {isAuthenticated() ? (
                <>
                  <span>
                    Welcome, {user?.email}
                  </span>
                  <a href="/dashboard">
                    My Dashboard
                  </a>
                  {isAdmin() && (
                    <a href="/admin">
                      Admin Panel
                    </a>
                  )}
                  <button>
                    Sign Out
                  </button>
                </>
              ) : (
                <>
                  <a href="/login">
                    Sign In
                  </a>
                  <a href="/rooms">
                    View Rooms
                  </a>
                </>
              )}
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section>
        <div className="container">
          <div className="hero-content text-center">
            <h1>
              Where Would You Like to Stay?
            </h1>
            <p>
              Experience comfort and hospitality in Ghana's premier guesthouse with modern amenities and exceptional service
            </p>

            {/* Quick Search */}
            <div>
              <div>
                <div className="form-group">
                  <label className="form-label">Check-in Date</label>
                  <input
                    type="date"
                    value={checkIn}
                    onChange={(e) => setCheckIn(e.target.value)}
                    className="form-input"
                    min={formatDate(new Date(), 'yyyy-MM-dd')}
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Check-out Date</label>
                  <input
                    type="date"
                    value={checkOut}
                    onChange={(e) => setCheckOut(e.target.value)}
                    className="form-input"
                    min={checkIn || formatDate(new Date(), 'yyyy-MM-dd')}
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Guests</label>
                  <select className="form-select">
                    <option value="1">1 Guest</option>
                    <option value="2">2 Guests</option>
                    <option value="3">3 Guests</option>
                    <option value="4">4+ Guests</option>
                  </select>
                </div>
                <div className="form-group">
                  <button
                    onClick={() => {
                      if (checkIn && checkOut) {
                        window.location.href = `/rooms?checkIn=${checkIn}&checkOut=${checkOut}&guests=1`;
                      } else {
                        window.location.href = '/rooms';
                      }
                    }}
                  >
                    🔍 Search Rooms
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Rooms */}
      <section>
        <div className="container">
          <div>
            <h2>
              Popular Hotels
            </h2>
            <p>
              Discover our most loved accommodations, carefully selected for their exceptional comfort and amenities
            </p>
          </div>

          {loading ? (
            <div>
              <div></div>
              <p>Loading featured rooms...</p>
            </div>
          ) : featuredRooms.length === 0 ? (
            <div>
              <div>🏨</div>
              <h3>No rooms available</h3>
              <p>
                Please check back later or contact us directly for availability.
              </p>
              <a href="/rooms">
                View All Rooms
              </a>
            </div>
          ) : (
            <div>
              {featuredRooms.map((room) => (
                <div key={room.id} className="room-card">
                  <div className="room-image">
                    <span>📷 Room Image</span>
                    <div className="room-price">
                      {formatCurrency(room.price_per_night)}/night
                    </div>
                  </div>
                  <div className="card-body">
                    <div>
                      <div>
                        <h3>
                          {room.room_type.charAt(0).toUpperCase() + room.room_type.slice(1)} Room
                        </h3>
                        <p>
                          Room {room.room_number} • Up to {room.capacity} guest{room.capacity > 1 ? 's' : ''}
                        </p>
                      </div>
                      <div>
                        <span className="text-warning">⭐</span>
                        <span>4.8</span>
                      </div>
                    </div>

                    <div>
                      {room.amenities?.slice(0, 4).map((amenity) => (
                        <span key={amenity} className="amenity-tag">
                          {amenity === 'WiFi' && '📶'}
                          {amenity === 'AC' && '❄️'}
                          {amenity === 'TV' && '📺'}
                          {amenity === 'Fridge' && '🧊'}
                          {amenity === 'Balcony' && '🏞️'}
                          {amenity}
                        </span>
                      ))}
                      {room.amenities?.length > 4 && (
                        <span className="amenity-tag">
                          +{room.amenities.length - 4} more
                        </span>
                      )}
                    </div>

                    <div>
                      <div>
                        <span>
                          {formatCurrency(room.price_per_night)}
                        </span>
                        <span>/night</span>
                      </div>
                      <a href={`/rooms/${room.id}`}>
                        Select Rooms
                      </a>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Features */}
      <section>
        <div className="container">
          <div>
            <h2>
              Why Choose Us?
            </h2>
            <p>
              Experience the perfect blend of comfort, convenience, and exceptional service
            </p>
          </div>

          <div>
            <div className="feature-card">
              <div>
                <span>🏨</span>
              </div>
              <h3>Prime Location</h3>
              <p>
                Located in the heart of Ghana with easy access to major attractions, business districts, and transportation hubs
              </p>
            </div>

            <div className="feature-card">
              <div>
                <span className="text-white">💳</span>
              </div>
              <h3>Easy Payment</h3>
              <p>
                Multiple secure payment options including Mobile Money, bank cards, and digital wallets for your convenience
              </p>
            </div>

            <div className="feature-card">
              <div className="feature-icon">
                <span>⭐</span>
              </div>
              <h3>Quality Service</h3>
              <p>
                24/7 customer service, premium amenities, and personalized attention to ensure your comfort and satisfaction
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <div>
            <div>
              <h3>
                🏨 Ghana Guesthouse
              </h3>
              <p>
                Experience comfort and hospitality in Ghana's premier guesthouse.
                Modern amenities, exceptional service, and unforgettable stays.
              </p>
              <div>
                <a href="#" className="text-gray-400 hover:text-white">📧</a>
                <a href="#" className="text-gray-400 hover:text-white">📱</a>
                <a href="#" className="text-gray-400 hover:text-white">🌐</a>
              </div>
            </div>

            <div>
              <h4>Quick Links</h4>
              <div>
                <a href="/rooms">Our Rooms</a>
                <a href="/about">About Us</a>
                <a href="/contact">Contact</a>
                <a href="/booking">Book Now</a>
              </div>
            </div>

            <div>
              <h4>Contact Info</h4>
              <div>
                <p>📍 Accra, Ghana</p>
                <p>📞 +233 XX XXX XXXX</p>
                <p>📧 <EMAIL></p>
              </div>
            </div>
          </div>

          <div>
            <p>
              &copy; 2024 Ghana Guesthouse Management System. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default HomePage;