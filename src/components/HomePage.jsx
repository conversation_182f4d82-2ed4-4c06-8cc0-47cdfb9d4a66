import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { formatCurrency } from '../utils/currency';
import { roomService } from '../services/roomService.js';
import { formatDate } from '../utils/date';

const HomePage = () => {
  const { user, isAuthenticated, isAdmin } = useAuth();
  const [checkIn, setCheckIn] = useState('');
  const [checkOut, setCheckOut] = useState('');
  const [featuredRooms, setFeaturedRooms] = useState([]);
  const [loading, setLoading] = useState(true);

  // Load featured rooms from Supabase
  useEffect(() => {
    loadFeaturedRooms();
  }, []);



  const loadFeaturedRooms = async () => {
    try {
      const result = await roomService.getAllRooms();
      if (result.data && result.data.length > 0) {
        // Show first 3 rooms as featured
        setFeaturedRooms(result.data.slice(0, 3));
      } else if (result.error) {
        console.error('Error loading featured rooms:', result.error);
        // If tables don't exist yet, show empty state
        setFeaturedRooms([]);
      } else {
        // No rooms found
        setFeaturedRooms([]);
      }
    } catch (error) {
      console.error('Error loading featured rooms:', error);
      setFeaturedRooms([]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Luxury Hotel Navigation */}
      <header className="hotel-navbar">
        <div className="container">
          <div className="flex justify-between items-center">
            <a href="/" className="hotel-brand">
              ✨ Ghana Guesthouse
            </a>
            <nav className="hotel-nav">
              {isAuthenticated() ? (
                <>
                  <span className="text-white text-sm">
                    Welcome, {user?.email}
                  </span>
                  <a href="/dashboard" className="hotel-nav-link">
                    My Dashboard
                  </a>
                  {isAdmin() && (
                    <a href="/admin" className="btn btn-primary btn-sm">
                      Admin Panel
                    </a>
                  )}
                  <button className="btn btn-outline btn-sm">
                    Sign Out
                  </button>
                </>
              ) : (
                <>
                  <a href="/login" className="hotel-nav-link">
                    Sign In
                  </a>
                  <a href="/rooms" className="btn btn-primary btn-sm">
                    View Rooms
                  </a>
                </>
              )}
            </nav>
          </div>
        </div>
      </header>

      {/* Luxury Hero Section */}
      <section className="hotel-hero">
        <div className="container">
          <div className="hotel-hero-content">
            <h1>
              Discover Luxury & Comfort in Ghana
            </h1>
            <p>
              Experience unparalleled hospitality in our premier guesthouse, where modern elegance meets traditional Ghanaian warmth
            </p>

            {/* Luxury Search Card */}
            <div className="hotel-search-card">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="hotel-form-group">
                  <label className="hotel-form-label">Check-in Date</label>
                  <input
                    type="date"
                    value={checkIn}
                    onChange={(e) => setCheckIn(e.target.value)}
                    className="hotel-form-input"
                    min={formatDate(new Date(), 'yyyy-MM-dd')}
                  />
                </div>
                <div className="hotel-form-group">
                  <label className="hotel-form-label">Check-out Date</label>
                  <input
                    type="date"
                    value={checkOut}
                    onChange={(e) => setCheckOut(e.target.value)}
                    className="hotel-form-input"
                    min={checkIn || formatDate(new Date(), 'yyyy-MM-dd')}
                  />
                </div>
                <div className="hotel-form-group">
                  <label className="hotel-form-label">Guests</label>
                  <select className="hotel-form-select">
                    <option value="1">1 Guest</option>
                    <option value="2">2 Guests</option>
                    <option value="3">3 Guests</option>
                    <option value="4">4+ Guests</option>
                  </select>
                </div>
                <div className="hotel-form-group">
                  <button
                    className="btn btn-primary btn-lg w-full"
                    onClick={() => {
                      if (checkIn && checkOut) {
                        window.location.href = `/rooms?checkIn=${checkIn}&checkOut=${checkOut}&guests=1`;
                      } else {
                        window.location.href = '/rooms';
                      }
                    }}
                  >
                    ✨ Search Rooms
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Rooms */}
      <section className="hotel-section bg-white">
        <div className="container">
          <div className="hotel-section-header">
            <h2 className="hotel-section-title">
              Exquisite Accommodations
            </h2>
            <p className="hotel-section-subtitle">
              Discover our carefully curated selection of luxury rooms, each designed to provide the ultimate comfort and elegance
            </p>
          </div>

          {loading ? (
            <div className="text-center py-16">
              <div className="hotel-spinner mb-6"></div>
              <p className="text-gray-600">Loading our finest accommodations...</p>
            </div>
          ) : featuredRooms.length === 0 ? (
            <div className="text-center py-16">
              <div className="text-6xl mb-8">🏨</div>
              <h3 className="text-2xl font-semibold mb-4">No rooms available</h3>
              <p className="text-gray-600 mb-8 max-w-md mx-auto">
                Please check back later or contact us directly for availability.
              </p>
              <a href="/rooms" className="btn btn-primary btn-lg">
                View All Rooms
              </a>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredRooms.map((room) => (
                <div key={room.id} className="hotel-room-card">
                  <div className="hotel-room-image">
                    <span className="text-gray-500">🏨 Luxury Room</span>
                    <div className="hotel-room-price">
                      {formatCurrency(room.price_per_night)}/night
                    </div>
                  </div>
                  <div className="hotel-room-content">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="hotel-room-title">
                          {room.room_type.charAt(0).toUpperCase() + room.room_type.slice(1)} Suite
                        </h3>
                        <p className="hotel-room-subtitle">
                          Room {room.room_number} • Up to {room.capacity} guest{room.capacity > 1 ? 's' : ''}
                        </p>
                      </div>
                      <div className="flex items-center gap-1">
                        <span className="text-lg">⭐</span>
                        <span className="text-sm font-semibold">4.9</span>
                      </div>
                    </div>

                    <div className="hotel-amenities">
                      {room.amenities?.slice(0, 4).map((amenity) => (
                        <span key={amenity} className="hotel-amenity-tag">
                          {amenity === 'WiFi' && '📶'}
                          {amenity === 'AC' && '❄️'}
                          {amenity === 'TV' && '📺'}
                          {amenity === 'Fridge' && '🧊'}
                          {amenity === 'Balcony' && '🏞️'}
                          {amenity}
                        </span>
                      ))}
                      {room.amenities?.length > 4 && (
                        <span className="hotel-amenity-tag">
                          +{room.amenities.length - 4} more
                        </span>
                      )}
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-2xl font-bold" style={{color: 'var(--accent-gold)'}}>
                          {formatCurrency(room.price_per_night)}
                        </span>
                        <span className="text-gray-500 text-sm">/night</span>
                      </div>
                      <a href={`/rooms/${room.id}`} className="btn btn-primary">
                        Reserve Now
                      </a>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Features */}
      <section className="hotel-section bg-gray-50">
        <div className="container">
          <div className="hotel-section-header">
            <h2 className="hotel-section-title">
              Why Choose Ghana Guesthouse?
            </h2>
            <p className="hotel-section-subtitle">
              Experience the perfect blend of luxury, comfort, and authentic Ghanaian hospitality
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="hotel-feature-card">
              <div className="hotel-feature-icon">
                <span>�️</span>
              </div>
              <h3 className="hotel-feature-title">Prime Location</h3>
              <p className="hotel-feature-description">
                Strategically located in the heart of Ghana with easy access to major attractions, business districts, and cultural landmarks
              </p>
            </div>

            <div className="hotel-feature-card">
              <div className="hotel-feature-icon">
                <span>�</span>
              </div>
              <h3 className="hotel-feature-title">Luxury Amenities</h3>
              <p className="hotel-feature-description">
                Premium facilities including spa services, fine dining, fitness center, and personalized concierge assistance
              </p>
            </div>

            <div className="hotel-feature-card">
              <div className="hotel-feature-icon">
                <span>🌟</span>
              </div>
              <h3 className="hotel-feature-title">Exceptional Service</h3>
              <p className="hotel-feature-description">
                24/7 dedicated staff, multilingual support, and personalized attention to ensure an unforgettable stay
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Luxury Footer */}
      <footer className="hotel-footer">
        <div className="container">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
            <div className="md:col-span-2">
              <h3>
                ✨ Ghana Guesthouse
              </h3>
              <p className="mb-6 max-w-md">
                Where luxury meets tradition. Experience the finest hospitality in Ghana with our world-class accommodations and personalized service.
              </p>
              <div className="flex gap-6">
                <a href="#" className="text-2xl">📧</a>
                <a href="#" className="text-2xl">📱</a>
                <a href="#" className="text-2xl">🌐</a>
                <a href="#" className="text-2xl">📍</a>
              </div>
            </div>

            <div>
              <h4>Explore</h4>
              <div className="space-y-4">
                <a href="/rooms" className="block">Luxury Suites</a>
                <a href="/dining" className="block">Fine Dining</a>
                <a href="/spa" className="block">Spa & Wellness</a>
                <a href="/events" className="block">Events & Meetings</a>
              </div>
            </div>

            <div>
              <h4>Contact</h4>
              <div className="space-y-4">
                <p>📍 Accra, Ghana</p>
                <p>📞 +233 XX XXX XXXX</p>
                <p>📧 <EMAIL></p>
                <p>🕒 24/7 Concierge Service</p>
              </div>
            </div>
          </div>

          <div className="hotel-footer-bottom">
            <p>
              &copy; 2024 Ghana Guesthouse. All rights reserved.
            </p>
            <p className="mt-2">
              Crafted with excellence for discerning travelers
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default HomePage;