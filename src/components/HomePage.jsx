import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { formatCurrency } from '../utils/currency';
import { roomService } from '../services/roomService.js';
import { formatDate } from '../utils/date';

const HomePage = () => {
  const { user, isAuthenticated, isAdmin } = useAuth();
  const [checkIn, setCheckIn] = useState('');
  const [checkOut, setCheckOut] = useState('');
  const [featuredRooms, setFeaturedRooms] = useState([]);
  const [loading, setLoading] = useState(true);

  // Load featured rooms from Supabase
  useEffect(() => {
    loadFeaturedRooms();
  }, []);

  // Test Supabase connection
  useEffect(() => {
    const testConnection = async () => {
      try {
        console.log('🔍 Testing Supabase connection...');
        const { data, error } = await roomService.getAllRooms();
        if (error) {
          console.error('❌ Supabase connection error:', error);
          console.log('💡 This might mean your database tables are not set up yet.');
          console.log('📋 Please run the database schema from database/schema.sql in your Supabase dashboard.');
          console.log('🔗 Go to: https://supabase.com/dashboard/project/YOUR_PROJECT_ID/sql');
        } else {
          console.log('✅ Supabase connection successful!');
          console.log('📊 Found', data?.length || 0, 'rooms in database');
          if (data?.length === 0) {
            console.log('💡 Database is empty. You may want to add some sample room data.');
          }
        }
      } catch (error) {
        console.error('❌ Connection test failed:', error);
      }
    };

    testConnection();
  }, []);

  const loadFeaturedRooms = async () => {
    try {
      const result = await roomService.getAllRooms();
      if (result.data && result.data.length > 0) {
        // Show first 3 rooms as featured
        setFeaturedRooms(result.data.slice(0, 3));
      } else if (result.error) {
        console.error('Error loading featured rooms:', result.error);
        // If tables don't exist yet, show empty state
        setFeaturedRooms([]);
      } else {
        // No rooms found
        setFeaturedRooms([]);
      }
    } catch (error) {
      console.error('Error loading featured rooms:', error);
      setFeaturedRooms([]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      {/* Header */}
      <header className="navbar">
        <div className="container">
          <div>
            <a href="/" className="navbar-brand">
              🏨 Ghana Guesthouse
            </a>
            <nav className="navbar-nav">
              {isAuthenticated() ? (
                <>
                  <span>
                    Welcome, {user?.email}
                  </span>
                  <a href="/dashboard">
                    My Dashboard
                  </a>
                  {isAdmin() && (
                    <a href="/admin">
                      Admin Panel
                    </a>
                  )}
                  <button>
                    Sign Out
                  </button>
                </>
              ) : (
                <>
                  <a href="/login">
                    Sign In
                  </a>
                  <a href="/rooms">
                    View Rooms
                  </a>
                </>
              )}
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section>
        <div className="container">
          <div className="hero-content text-center">
            <h1>
              Where Would You Like to Stay?
            </h1>
            <p>
              Experience comfort and hospitality in Ghana's premier guesthouse with modern amenities and exceptional service
            </p>

            {/* Quick Search */}
            <div>
              <div>
                <div className="form-group">
                  <label className="form-label">Check-in Date</label>
                  <input
                    type="date"
                    value={checkIn}
                    onChange={(e) => setCheckIn(e.target.value)}
                    className="form-input"
                    min={formatDate(new Date(), 'yyyy-MM-dd')}
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Check-out Date</label>
                  <input
                    type="date"
                    value={checkOut}
                    onChange={(e) => setCheckOut(e.target.value)}
                    className="form-input"
                    min={checkIn || formatDate(new Date(), 'yyyy-MM-dd')}
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">Guests</label>
                  <select className="form-select">
                    <option value="1">1 Guest</option>
                    <option value="2">2 Guests</option>
                    <option value="3">3 Guests</option>
                    <option value="4">4+ Guests</option>
                  </select>
                </div>
                <div className="form-group">
                  <button
                    onClick={() => {
                      if (checkIn && checkOut) {
                        window.location.href = `/rooms?checkIn=${checkIn}&checkOut=${checkOut}&guests=1`;
                      } else {
                        window.location.href = '/rooms';
                      }
                    }}
                  >
                    🔍 Search Rooms
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Rooms */}
      <section className="py-20 bg-white">
        <div className="container">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Popular Hotels
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Discover our most loved accommodations, carefully selected for their exceptional comfort and amenities
            </p>
          </div>

          {loading ? (
            <div className="text-center py-16">
              <div className="spinner spinner-lg mx-auto mb-6"></div>
              <p className="text-gray-600">Loading featured rooms...</p>
            </div>
          ) : featuredRooms.length === 0 ? (
            <div className="text-center py-16">
              <div className="text-6xl mb-6">🏨</div>
              <h3 className="text-2xl font-semibold mb-4">No rooms available</h3>
              <p className="text-gray-600 mb-8 max-w-md mx-auto">
                Please check back later or contact us directly for availability.
              </p>
              <a href="/rooms" className="btn btn-primary btn-lg">
                View All Rooms
              </a>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredRooms.map((room) => (
                <div key={room.id} className="room-card">
                  <div className="room-image">
                    <span className="text-gray-500">📷 Room Image</span>
                    <div className="room-price">
                      {formatCurrency(room.price_per_night)}/night
                    </div>
                  </div>
                  <div className="card-body">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="text-xl font-semibold mb-1">
                          {room.room_type.charAt(0).toUpperCase() + room.room_type.slice(1)} Room
                        </h3>
                        <p className="text-gray-500 text-sm">
                          Room {room.room_number} • Up to {room.capacity} guest{room.capacity > 1 ? 's' : ''}
                        </p>
                      </div>
                      <div className="flex items-center gap-1">
                        <span className="text-warning">⭐</span>
                        <span className="text-sm font-medium">4.8</span>
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-2 mb-6">
                      {room.amenities?.slice(0, 4).map((amenity) => (
                        <span key={amenity} className="amenity-tag">
                          {amenity === 'WiFi' && '📶'}
                          {amenity === 'AC' && '❄️'}
                          {amenity === 'TV' && '📺'}
                          {amenity === 'Fridge' && '🧊'}
                          {amenity === 'Balcony' && '🏞️'}
                          {amenity}
                        </span>
                      ))}
                      {room.amenities?.length > 4 && (
                        <span className="amenity-tag">
                          +{room.amenities.length - 4} more
                        </span>
                      )}
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-2xl font-bold text-primary">
                          {formatCurrency(room.price_per_night)}
                        </span>
                        <span className="text-gray-500 text-sm">/night</span>
                      </div>
                      <a href={`/rooms/${room.id}`} className="btn btn-primary">
                        Select Rooms
                      </a>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Features */}
      <section className="py-20 bg-gray-50">
        <div className="container">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Why Choose Us?
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Experience the perfect blend of comfort, convenience, and exceptional service
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="feature-card">
              <div className="feature-icon bg-primary">
                <span className="text-white">🏨</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Prime Location</h3>
              <p className="text-gray-600">
                Located in the heart of Ghana with easy access to major attractions, business districts, and transportation hubs
              </p>
            </div>

            <div className="feature-card">
              <div className="feature-icon bg-success">
                <span className="text-white">💳</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Easy Payment</h3>
              <p className="text-gray-600">
                Multiple secure payment options including Mobile Money, bank cards, and digital wallets for your convenience
              </p>
            </div>

            <div className="feature-card">
              <div className="feature-icon bg-warning">
                <span className="text-white">⭐</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Quality Service</h3>
              <p className="text-gray-600">
                24/7 customer service, premium amenities, and personalized attention to ensure your comfort and satisfaction
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer py-12">
        <div className="container">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
            <div className="md:col-span-2">
              <h3 className="text-xl font-bold text-white mb-4">
                🏨 Ghana Guesthouse
              </h3>
              <p className="text-gray-400 mb-4 max-w-md">
                Experience comfort and hospitality in Ghana's premier guesthouse.
                Modern amenities, exceptional service, and unforgettable stays.
              </p>
              <div className="flex gap-4">
                <a href="#" className="text-gray-400 hover:text-white">📧</a>
                <a href="#" className="text-gray-400 hover:text-white">📱</a>
                <a href="#" className="text-gray-400 hover:text-white">🌐</a>
              </div>
            </div>

            <div>
              <h4 className="font-semibold text-white mb-4">Quick Links</h4>
              <div className="space-y-2">
                <a href="/rooms" className="block text-gray-400 hover:text-white">Our Rooms</a>
                <a href="/about" className="block text-gray-400 hover:text-white">About Us</a>
                <a href="/contact" className="block text-gray-400 hover:text-white">Contact</a>
                <a href="/booking" className="block text-gray-400 hover:text-white">Book Now</a>
              </div>
            </div>

            <div>
              <h4 className="font-semibold text-white mb-4">Contact Info</h4>
              <div className="space-y-2 text-gray-400">
                <p>📍 Accra, Ghana</p>
                <p>📞 +233 XX XXX XXXX</p>
                <p>📧 <EMAIL></p>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-700 pt-8 text-center">
            <p className="text-gray-400">
              &copy; 2024 Ghana Guesthouse Management System. All rights reserved.
            </p>
            <p className="text-gray-500 mt-2 text-sm">
              Built with ❤️ for Ghana's hospitality industry
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default HomePage;