// Advanced reports and analytics page

import { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { analyticsService } from '../../services/analyticsService';
import { reviewService } from '../../services/reviewService';
import { formatCurrency } from '../../utils/currency';
import { formatDate } from '../../utils/date';
import toast from 'react-hot-toast';

const AdminReports = () => {
  const { isAdmin, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('revenue');
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0], // Start of year
    endDate: new Date().toISOString().split('T')[0] // Today
  });

  const [analytics, setAnalytics] = useState({
    revenue: null,
    occupancy: null,
    guests: null,
    trends: null,
    roomPerformance: null,
    reviews: null
  });

  useEffect(() => {
    if (!isAuthenticated()) {
      navigate('/login');
      return;
    }

    if (!isAdmin()) {
      toast.error('Access denied. Admin privileges required.');
      navigate('/');
      return;
    }

    loadAnalytics();
  }, [isAuthenticated, isAdmin, dateRange]);

  const loadAnalytics = async () => {
    setLoading(true);
    try {
      const [
        revenueResult,
        occupancyResult,
        guestResult,
        trendsResult,
        roomPerformanceResult,
        reviewsResult
      ] = await Promise.all([
        analyticsService.getRevenueAnalytics(dateRange.startDate, dateRange.endDate),
        analyticsService.getOccupancyAnalytics(dateRange.startDate, dateRange.endDate),
        analyticsService.getGuestAnalytics(dateRange.startDate, dateRange.endDate),
        analyticsService.getPerformanceTrends(12),
        analyticsService.getRoomPerformance(dateRange.startDate, dateRange.endDate),
        reviewService.getOverallRatingSummary()
      ]);

      setAnalytics({
        revenue: revenueResult.data,
        occupancy: occupancyResult.data,
        guests: guestResult.data,
        trends: trendsResult.data,
        roomPerformance: roomPerformanceResult.data,
        reviews: reviewsResult.data
      });
    } catch (error) {
      console.error('Error loading analytics:', error);
      toast.error('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const handleDateRangeChange = (key, value) => {
    setDateRange(prev => ({ ...prev, [key]: value }));
  };

  const exportReport = () => {
    // Simple CSV export functionality
    const csvData = [];
    
    if (analytics.revenue) {
      csvData.push(['Revenue Report']);
      csvData.push(['Total Revenue', formatCurrency(analytics.revenue.totalRevenue)]);
      csvData.push(['Pending Revenue', formatCurrency(analytics.revenue.pendingRevenue)]);
      csvData.push(['Total Bookings', analytics.revenue.totalBookings]);
      csvData.push(['Confirmed Bookings', analytics.revenue.confirmedBookings]);
      csvData.push(['Average Booking Value', formatCurrency(analytics.revenue.averageBookingValue)]);
      csvData.push([]);
    }

    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `guesthouse-report-${dateRange.startDate}-to-${dateRange.endDate}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="spinner mx-auto mb-4"></div>
          <p className="text-gray-600">Loading analytics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <a href="/admin" className="text-gray-600 hover:text-primary mr-4">
                ← Dashboard
              </a>
              <h1 className="text-2xl font-bold text-primary">Reports & Analytics</h1>
            </div>
            <button onClick={exportReport} className="btn btn-outline">
              Export Report
            </button>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-6">
        {/* Date Range Filter */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h3 className="text-lg font-semibold mb-4">Date Range</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="form-group">
              <label className="form-label">Start Date</label>
              <input
                type="date"
                value={dateRange.startDate}
                onChange={(e) => handleDateRangeChange('startDate', e.target.value)}
                className="form-input"
              />
            </div>
            <div className="form-group">
              <label className="form-label">End Date</label>
              <input
                type="date"
                value={dateRange.endDate}
                onChange={(e) => handleDateRangeChange('endDate', e.target.value)}
                className="form-input"
              />
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-md mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'revenue', label: 'Revenue' },
                { id: 'occupancy', label: 'Occupancy' },
                { id: 'guests', label: 'Guests' },
                { id: 'rooms', label: 'Room Performance' },
                { id: 'reviews', label: 'Reviews' }
              ].map(tab => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-primary text-primary'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {/* Revenue Tab */}
            {activeTab === 'revenue' && analytics.revenue && (
              <div>
                <h3 className="text-xl font-semibold mb-6">Revenue Analytics</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  <div className="bg-green-50 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold text-green-800">Total Revenue</h4>
                    <p className="text-3xl font-bold text-green-600">
                      {formatCurrency(analytics.revenue.totalRevenue)}
                    </p>
                  </div>
                  <div className="bg-yellow-50 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold text-yellow-800">Pending Revenue</h4>
                    <p className="text-3xl font-bold text-yellow-600">
                      {formatCurrency(analytics.revenue.pendingRevenue)}
                    </p>
                  </div>
                  <div className="bg-blue-50 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold text-blue-800">Average Booking</h4>
                    <p className="text-3xl font-bold text-blue-600">
                      {formatCurrency(analytics.revenue.averageBookingValue)}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold mb-4">Booking Statistics</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Total Bookings:</span>
                        <span className="font-semibold">{analytics.revenue.totalBookings}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Confirmed:</span>
                        <span className="font-semibold text-green-600">{analytics.revenue.confirmedBookings}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Cancelled:</span>
                        <span className="font-semibold text-red-600">{analytics.revenue.cancelledBookings}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Completed:</span>
                        <span className="font-semibold text-blue-600">{analytics.revenue.completedBookings}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Conversion Rate:</span>
                        <span className="font-semibold">{analytics.revenue.conversionRate.toFixed(1)}%</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold mb-4">Monthly Breakdown</h4>
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {analytics.revenue.monthlyData.map(month => (
                        <div key={month.month} className="flex justify-between">
                          <span>{month.month}:</span>
                          <span className="font-semibold">{formatCurrency(month.revenue)}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Occupancy Tab */}
            {activeTab === 'occupancy' && analytics.occupancy && (
              <div>
                <h3 className="text-xl font-semibold mb-6">Occupancy Analytics</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  <div className="bg-blue-50 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold text-blue-800">Total Rooms</h4>
                    <p className="text-3xl font-bold text-blue-600">{analytics.occupancy.totalRooms}</p>
                  </div>
                  <div className="bg-green-50 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold text-green-800">Average Occupancy</h4>
                    <p className="text-3xl font-bold text-green-600">
                      {analytics.occupancy.averageOccupancyRate.toFixed(1)}%
                    </p>
                  </div>
                  <div className="bg-purple-50 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold text-purple-800">Total Days</h4>
                    <p className="text-3xl font-bold text-purple-600">{analytics.occupancy.totalDays}</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold mb-4">By Room Type</h4>
                    <div className="space-y-3">
                      {Object.entries(analytics.occupancy.roomTypeOccupancy).map(([type, data]) => (
                        <div key={type} className="border-b pb-2">
                          <div className="flex justify-between items-center">
                            <span className="font-medium capitalize">{type}</span>
                            <span className="font-semibold">{data.occupancyRate.toFixed(1)}%</span>
                          </div>
                          <div className="text-sm text-gray-600">
                            {data.totalRooms} rooms • {data.occupiedNights} occupied nights
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold mb-4">Individual Room Performance</h4>
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {analytics.occupancy.roomOccupancy
                        .sort((a, b) => b.occupancyRate - a.occupancyRate)
                        .map(room => (
                          <div key={room.roomId} className="flex justify-between">
                            <span>Room {room.roomNumber}:</span>
                            <span className="font-semibold">{room.occupancyRate.toFixed(1)}%</span>
                          </div>
                        ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Guest Analytics Tab */}
            {activeTab === 'guests' && analytics.guests && (
              <div>
                <h3 className="text-xl font-semibold mb-6">Guest Analytics</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                  <div className="bg-blue-50 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold text-blue-800">Unique Guests</h4>
                    <p className="text-3xl font-bold text-blue-600">{analytics.guests.uniqueGuests}</p>
                  </div>
                  <div className="bg-green-50 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold text-green-800">Total Guest Nights</h4>
                    <p className="text-3xl font-bold text-green-600">{analytics.guests.totalGuestNights}</p>
                  </div>
                  <div className="bg-purple-50 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold text-purple-800">Repeat Guests</h4>
                    <p className="text-3xl font-bold text-purple-600">{analytics.guests.repeatGuestCount}</p>
                  </div>
                  <div className="bg-yellow-50 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold text-yellow-800">Repeat Rate</h4>
                    <p className="text-3xl font-bold text-yellow-600">{analytics.guests.repeatGuestRate.toFixed(1)}%</p>
                  </div>
                </div>

                <div className="bg-gray-50 p-6 rounded-lg">
                  <h4 className="text-lg font-semibold mb-4">Top Guests by Spending</h4>
                  <div className="overflow-x-auto">
                    <table className="min-w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-2">Guest</th>
                          <th className="text-left py-2">Total Spent</th>
                          <th className="text-left py-2">Bookings</th>
                          <th className="text-left py-2">Average per Booking</th>
                        </tr>
                      </thead>
                      <tbody>
                        {analytics.guests.topGuests.slice(0, 10).map((guest, index) => (
                          <tr key={guest.guestId} className="border-b">
                            <td className="py-2">
                              {guest.guestInfo?.full_name || 'Guest'} 
                              <div className="text-sm text-gray-600">{guest.guestInfo?.email}</div>
                            </td>
                            <td className="py-2 font-semibold">{formatCurrency(guest.totalSpent)}</td>
                            <td className="py-2">{guest.bookingCount}</td>
                            <td className="py-2">{formatCurrency(guest.totalSpent / guest.bookingCount)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}

            {/* Room Performance Tab */}
            {activeTab === 'rooms' && analytics.roomPerformance && (
              <div>
                <h3 className="text-xl font-semibold mb-6">Room Performance</h3>
                
                <div className="overflow-x-auto">
                  <table className="min-w-full bg-white">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Room</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Revenue</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Bookings</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Avg Stay</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Cancel Rate</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {analytics.roomPerformance.map((room) => (
                        <tr key={room.roomId}>
                          <td className="px-6 py-4 whitespace-nowrap font-medium">
                            {room.roomInfo?.room_number}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap capitalize">
                            {room.roomInfo?.room_type}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap font-semibold">
                            {formatCurrency(room.totalRevenue)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {room.totalBookings}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {room.averageStayLength.toFixed(1)} nights
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 rounded text-xs ${
                              room.cancellationRate > 20 
                                ? 'bg-red-100 text-red-800' 
                                : room.cancellationRate > 10 
                                ? 'bg-yellow-100 text-yellow-800' 
                                : 'bg-green-100 text-green-800'
                            }`}>
                              {room.cancellationRate.toFixed(1)}%
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Reviews Tab */}
            {activeTab === 'reviews' && analytics.reviews && (
              <div>
                <h3 className="text-xl font-semibold mb-6">Review Analytics</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  <div className="bg-yellow-50 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold text-yellow-800">Average Rating</h4>
                    <p className="text-3xl font-bold text-yellow-600">
                      {analytics.reviews.averageRating.toFixed(1)} ⭐
                    </p>
                  </div>
                  <div className="bg-blue-50 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold text-blue-800">Total Reviews</h4>
                    <p className="text-3xl font-bold text-blue-600">{analytics.reviews.totalReviews}</p>
                  </div>
                  <div className="bg-green-50 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold text-green-800">Recommendation Rate</h4>
                    <p className="text-3xl font-bold text-green-600">
                      {analytics.reviews.recommendationRate.toFixed(1)}%
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold mb-4">Rating Distribution</h4>
                    <div className="space-y-2">
                      {Object.entries(analytics.reviews.ratingBreakdown)
                        .sort(([a], [b]) => b - a)
                        .map(([rating, count]) => (
                          <div key={rating} className="flex items-center">
                            <span className="w-8">{rating}⭐</span>
                            <div className="flex-1 mx-3 bg-gray-200 rounded-full h-4">
                              <div 
                                className="bg-yellow-500 h-4 rounded-full" 
                                style={{ 
                                  width: `${analytics.reviews.totalReviews > 0 ? (count / analytics.reviews.totalReviews) * 100 : 0}%` 
                                }}
                              ></div>
                            </div>
                            <span className="w-8 text-sm">{count}</span>
                          </div>
                        ))}
                    </div>
                  </div>

                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold mb-4">Category Averages</h4>
                    <div className="space-y-3">
                      {Object.entries(analytics.reviews.categoryAverages).map(([category, average]) => (
                        <div key={category} className="flex justify-between items-center">
                          <span className="capitalize">{category}:</span>
                          <span className="font-semibold">{average.toFixed(1)} ⭐</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminReports;
