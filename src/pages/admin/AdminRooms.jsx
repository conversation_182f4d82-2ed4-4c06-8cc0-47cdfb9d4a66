// Admin room management page

import { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { roomService } from '../../services/roomService';
import { supabase } from '../../services/supabase';
import { formatCurrency } from '../../utils/currency';
import { ROOM_TYPES, AMENITIES } from '../../constants';
import toast from 'react-hot-toast';

const AdminRooms = () => {
  const { isAdmin, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  const [rooms, setRooms] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingRoom, setEditingRoom] = useState(null);
  const [formData, setFormData] = useState({
    room_number: '',
    room_type: ROOM_TYPES.SINGLE,
    price_per_night: '',
    capacity: 1,
    amenities: [],
    description: '',
    is_available: true
  });

  useEffect(() => {
    if (!isAuthenticated()) {
      navigate('/login');
      return;
    }

    if (!isAdmin()) {
      toast.error('Access denied. Admin privileges required.');
      navigate('/');
      return;
    }

    loadRooms();
  }, [isAuthenticated, isAdmin]);

  const loadRooms = async () => {
    setLoading(true);
    try {
      // For admin, we want to see all rooms, not just available ones
      const { data, error } = await supabase
        .from('rooms')
        .select('*')
        .order('room_number');

      if (error) throw error;
      setRooms(data || []);
    } catch (error) {
      console.error('Error loading rooms:', error);
      toast.error('Failed to load rooms');
    } finally {
      setLoading(false);
    }
  };

  const handleFormChange = (key, value) => {
    setFormData(prev => ({ ...prev, [key]: value }));
  };

  const handleAmenityToggle = (amenity) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.includes(amenity)
        ? prev.amenities.filter(a => a !== amenity)
        : [...prev.amenities, amenity]
    }));
  };

  const resetForm = () => {
    setFormData({
      room_number: '',
      room_type: ROOM_TYPES.SINGLE,
      price_per_night: '',
      capacity: 1,
      amenities: [],
      description: '',
      is_available: true
    });
    setEditingRoom(null);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.room_number || !formData.price_per_night) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      const roomData = {
        ...formData,
        price_per_night: parseFloat(formData.price_per_night)
      };

      let result;
      if (editingRoom) {
        // Update existing room
        const { data, error } = await supabase
          .from('rooms')
          .update(roomData)
          .eq('id', editingRoom.id)
          .select()
          .single();

        result = { data, error };
      } else {
        // Create new room
        const { data, error } = await supabase
          .from('rooms')
          .insert(roomData)
          .select()
          .single();

        result = { data, error };
      }

      if (result.error) throw result.error;

      toast.success(editingRoom ? 'Room updated successfully' : 'Room created successfully');
      setShowAddModal(false);
      resetForm();
      loadRooms();
    } catch (error) {
      console.error('Error saving room:', error);
      toast.error('Failed to save room');
    }
  };

  const handleEdit = (room) => {
    setFormData({
      room_number: room.room_number,
      room_type: room.room_type,
      price_per_night: room.price_per_night.toString(),
      capacity: room.capacity,
      amenities: room.amenities || [],
      description: room.description || '',
      is_available: room.is_available
    });
    setEditingRoom(room);
    setShowAddModal(true);
  };

  const handleDelete = async (roomId) => {
    if (!confirm('Are you sure you want to delete this room?')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('rooms')
        .delete()
        .eq('id', roomId);

      if (error) throw error;

      toast.success('Room deleted successfully');
      loadRooms();
    } catch (error) {
      console.error('Error deleting room:', error);
      toast.error('Failed to delete room');
    }
  };

  const toggleAvailability = async (room) => {
    try {
      const { error } = await supabase
        .from('rooms')
        .update({ is_available: !room.is_available })
        .eq('id', room.id);

      if (error) throw error;

      toast.success(`Room ${room.is_available ? 'disabled' : 'enabled'} successfully`);
      loadRooms();
    } catch (error) {
      console.error('Error updating room availability:', error);
      toast.error('Failed to update room availability');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="spinner mx-auto mb-4"></div>
          <p className="text-gray-600">Loading rooms...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <a href="/admin" className="text-gray-600 hover:text-primary mr-4">
                ← Dashboard
              </a>
              <h1 className="text-2xl font-bold text-primary">Room Management</h1>
            </div>
            <button
              onClick={() => {
                resetForm();
                setShowAddModal(true);
              }}
              className="btn btn-primary"
            >
              Add New Room
            </button>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-6">
        {/* Rooms Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {rooms.map((room) => (
            <div key={room.id} className={`card ${!room.is_available ? 'opacity-60' : ''}`}>
              <div className="card-body">
                <div className="flex justify-between items-start mb-3">
                  <h3 className="text-lg font-semibold">
                    Room {room.room_number}
                  </h3>
                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      room.is_available 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {room.is_available ? 'Available' : 'Disabled'}
                    </span>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <p><strong>Type:</strong> {room.room_type.charAt(0).toUpperCase() + room.room_type.slice(1)}</p>
                  <p><strong>Capacity:</strong> {room.capacity} guest{room.capacity > 1 ? 's' : ''}</p>
                  <p><strong>Price:</strong> {formatCurrency(room.price_per_night)}/night</p>
                  {room.description && (
                    <p><strong>Description:</strong> {room.description}</p>
                  )}
                </div>

                {room.amenities && room.amenities.length > 0 && (
                  <div className="mb-4">
                    <p className="font-medium mb-2">Amenities:</p>
                    <div className="flex flex-wrap gap-1">
                      {room.amenities.map((amenity) => (
                        <span key={amenity} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                          {amenity}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex gap-2">
                  <button
                    onClick={() => handleEdit(room)}
                    className="btn btn-outline flex-1"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => toggleAvailability(room)}
                    className={`btn flex-1 ${
                      room.is_available 
                        ? 'btn-outline text-red-600 border-red-600 hover:bg-red-600 hover:text-white' 
                        : 'btn-outline text-green-600 border-green-600 hover:bg-green-600 hover:text-white'
                    }`}
                  >
                    {room.is_available ? 'Disable' : 'Enable'}
                  </button>
                  <button
                    onClick={() => handleDelete(room.id)}
                    className="btn btn-outline text-red-600 border-red-600 hover:bg-red-600 hover:text-white"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {rooms.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🏨</div>
            <h3 className="text-xl font-semibold mb-2">No rooms found</h3>
            <p className="text-gray-600 mb-4">Start by adding your first room.</p>
            <button
              onClick={() => {
                resetForm();
                setShowAddModal(true);
              }}
              className="btn btn-primary"
            >
              Add First Room
            </button>
          </div>
        )}
      </div>

      {/* Add/Edit Room Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full max-h-screen overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">
                  {editingRoom ? 'Edit Room' : 'Add New Room'}
                </h3>
                <button
                  onClick={() => {
                    setShowAddModal(false);
                    resetForm();
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="form-group">
                  <label className="form-label">Room Number *</label>
                  <input
                    type="text"
                    value={formData.room_number}
                    onChange={(e) => handleFormChange('room_number', e.target.value)}
                    className="form-input"
                    placeholder="e.g., R001"
                    required
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">Room Type *</label>
                  <select
                    value={formData.room_type}
                    onChange={(e) => handleFormChange('room_type', e.target.value)}
                    className="form-input"
                    required
                  >
                    <option value={ROOM_TYPES.SINGLE}>Single</option>
                    <option value={ROOM_TYPES.DOUBLE}>Double</option>
                    <option value={ROOM_TYPES.SUITE}>Suite</option>
                  </select>
                </div>

                <div className="form-group">
                  <label className="form-label">Price per Night (GHS) *</label>
                  <input
                    type="number"
                    value={formData.price_per_night}
                    onChange={(e) => handleFormChange('price_per_night', e.target.value)}
                    className="form-input"
                    placeholder="150.00"
                    min="0"
                    step="0.01"
                    required
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">Capacity *</label>
                  <select
                    value={formData.capacity}
                    onChange={(e) => handleFormChange('capacity', parseInt(e.target.value))}
                    className="form-input"
                    required
                  >
                    <option value={1}>1 Guest</option>
                    <option value={2}>2 Guests</option>
                    <option value={3}>3 Guests</option>
                    <option value={4}>4 Guests</option>
                  </select>
                </div>

                <div className="form-group">
                  <label className="form-label">Amenities</label>
                  <div className="grid grid-cols-2 gap-2">
                    {Object.entries(AMENITIES).map(([key, label]) => (
                      <label key={key} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.amenities.includes(key)}
                          onChange={() => handleAmenityToggle(key)}
                          className="mr-2"
                        />
                        <span className="text-sm">{label}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <div className="form-group">
                  <label className="form-label">Description</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleFormChange('description', e.target.value)}
                    className="form-input"
                    rows="3"
                    placeholder="Room description..."
                  />
                </div>

                <div className="form-group">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.is_available}
                      onChange={(e) => handleFormChange('is_available', e.target.checked)}
                      className="mr-2"
                    />
                    <span>Room is available for booking</span>
                  </label>
                </div>

                <div className="flex gap-3">
                  <button type="submit" className="btn btn-primary flex-1">
                    {editingRoom ? 'Update Room' : 'Create Room'}
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowAddModal(false);
                      resetForm();
                    }}
                    className="btn btn-outline flex-1"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminRooms;
