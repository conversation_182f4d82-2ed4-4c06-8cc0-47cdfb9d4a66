// Admin dashboard with overview and analytics

import { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { bookingService } from '../../services/bookingService';
import { roomService } from '../../services/roomService';
import { formatCurrency } from '../../utils/currency';
import { formatDate } from '../../utils/date';
import toast from 'react-hot-toast';

const AdminDashboard = () => {
  const { user, isAdmin, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalBookings: 0,
    confirmedBookings: 0,
    totalRevenue: 0,
    pendingRevenue: 0
  });
  const [recentBookings, setRecentBookings] = useState([]);
  const [upcomingCheckIns, setUpcomingCheckIns] = useState([]);
  const [upcomingCheckOuts, setUpcomingCheckOuts] = useState([]);
  const [roomStats, setRoomStats] = useState({
    totalRooms: 0,
    availableRooms: 0,
    occupiedRooms: 0
  });

  useEffect(() => {
    if (!isAuthenticated()) {
      navigate('/login');
      return;
    }

    if (!isAdmin()) {
      toast.error('Access denied. Admin privileges required.');
      navigate('/');
      return;
    }

    loadDashboardData();
  }, [isAuthenticated, isAdmin]);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // Load booking statistics
      const statsResult = await bookingService.getBookingStats();
      if (statsResult.data) {
        setStats(statsResult.data);
      }

      // Load recent bookings
      const bookingsResult = await bookingService.getAllBookings();
      if (bookingsResult.data) {
        setRecentBookings(bookingsResult.data.slice(0, 5)); // Latest 5 bookings
      }

      // Load upcoming check-ins
      const checkInsResult = await bookingService.getUpcomingCheckIns(7);
      if (checkInsResult.data) {
        setUpcomingCheckIns(checkInsResult.data);
      }

      // Load upcoming check-outs
      const checkOutsResult = await bookingService.getUpcomingCheckOuts(7);
      if (checkOutsResult.data) {
        setUpcomingCheckOuts(checkOutsResult.data);
      }

      // Load room statistics
      const roomsResult = await roomService.getAllRooms();
      if (roomsResult.data) {
        const totalRooms = roomsResult.data.length;
        const availableRooms = roomsResult.data.filter(room => room.is_available).length;
        setRoomStats({
          totalRooms,
          availableRooms,
          occupiedRooms: totalRooms - availableRooms
        });
      }

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const getBookingStatusColor = (status) => {
    switch (status) {
      case 'confirmed': return 'text-green-600 bg-green-100';
      case 'cancelled': return 'text-red-600 bg-red-100';
      case 'completed': return 'text-blue-600 bg-blue-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="spinner mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-primary">Admin Dashboard</h1>
            <nav className="flex items-center space-x-4">
              <a href="/admin/rooms" className="text-gray-600 hover:text-primary">Rooms</a>
              <a href="/admin/bookings" className="text-gray-600 hover:text-primary">Bookings</a>
              <a href="/admin/guests" className="text-gray-600 hover:text-primary">Guests</a>
              <a href="/" className="btn btn-outline">Back to Site</a>
            </nav>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-6">
        {/* Welcome Section */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-2">
            Welcome back, {user?.user_metadata?.full_name || 'Admin'}!
          </h2>
          <p className="text-gray-600">
            Here's an overview of your guesthouse operations.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100 text-blue-600">
                📊
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Bookings</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.totalBookings}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 text-green-600">
                ✅
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Confirmed</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.confirmedBookings}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-primary bg-opacity-10 text-primary">
                💰
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-2xl font-semibold text-gray-900">{formatCurrency(stats.totalRevenue)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-yellow-100 text-yellow-600">
                ⏳
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending Revenue</p>
                <p className="text-2xl font-semibold text-gray-900">{formatCurrency(stats.pendingRevenue)}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Room Statistics */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h3 className="text-lg font-semibold mb-4">Room Overview</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-2xl font-bold text-gray-900">{roomStats.totalRooms}</p>
              <p className="text-sm text-gray-600">Total Rooms</p>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <p className="text-2xl font-bold text-green-600">{roomStats.availableRooms}</p>
              <p className="text-sm text-gray-600">Available</p>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <p className="text-2xl font-bold text-red-600">{roomStats.occupiedRooms}</p>
              <p className="text-sm text-gray-600">Occupied</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Bookings */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Recent Bookings</h3>
              <a href="/admin/bookings" className="text-primary hover:text-accent text-sm">
                View All →
              </a>
            </div>
            
            {recentBookings.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No recent bookings</p>
            ) : (
              <div className="space-y-3">
                {recentBookings.map((booking) => (
                  <div key={booking.id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <div>
                      <p className="font-medium">{booking.guest?.full_name || 'Guest'}</p>
                      <p className="text-sm text-gray-600">
                        Room {booking.room?.room_number} • {formatDate(booking.check_in_date)}
                      </p>
                    </div>
                    <div className="text-right">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getBookingStatusColor(booking.booking_status)}`}>
                        {booking.booking_status}
                      </span>
                      <p className="text-sm font-medium mt-1">{formatCurrency(booking.total_amount)}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Upcoming Check-ins */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold mb-4">Upcoming Check-ins (7 days)</h3>
            
            {upcomingCheckIns.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No upcoming check-ins</p>
            ) : (
              <div className="space-y-3">
                {upcomingCheckIns.map((booking) => (
                  <div key={booking.id} className="flex items-center justify-between p-3 bg-green-50 rounded">
                    <div>
                      <p className="font-medium">{booking.guest?.full_name || 'Guest'}</p>
                      <p className="text-sm text-gray-600">
                        Room {booking.room?.room_number}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{formatDate(booking.check_in_date)}</p>
                      <p className="text-xs text-gray-600">{booking.guest_count} guest{booking.guest_count > 1 ? 's' : ''}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow-md p-6 mt-6">
          <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <a href="/admin/bookings/new" className="btn btn-primary text-center">
              New Booking
            </a>
            <a href="/admin/rooms" className="btn btn-outline text-center">
              Manage Rooms
            </a>
            <a href="/admin/guests" className="btn btn-outline text-center">
              View Guests
            </a>
            <a href="/admin/reports" className="btn btn-outline text-center">
              Generate Reports
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
