// User dashboard for managing bookings and profile

import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';
import { bookingService } from '../services/bookingService';
import { formatCurrency } from '../utils/currency';
import { formatDate, formatDateRange } from '../utils/date';
import toast from 'react-hot-toast';

const UserDashboard = () => {
  const { user, isAuthenticated, signOut, updateProfile } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const [activeTab, setActiveTab] = useState('bookings');
  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [profileData, setProfileData] = useState({
    fullName: '',
    phone: ''
  });
  const [editingProfile, setEditingProfile] = useState(false);

  useEffect(() => {
    if (!isAuthenticated()) {
      navigate('/login');
      return;
    }

    loadUserData();

    // Show success message for new booking
    if (location.state?.showBookingSuccess) {
      toast.success('Booking confirmed! You will receive a confirmation email shortly.');
    }
  }, [isAuthenticated, user]);

  const loadUserData = async () => {
    setLoading(true);
    try {
      // Load user bookings
      const bookingsResult = await bookingService.getUserBookings(user.id);
      if (bookingsResult.data) {
        setBookings(bookingsResult.data);
      }

      // Set profile data
      setProfileData({
        fullName: user.user_metadata?.full_name || '',
        phone: user.user_metadata?.phone || ''
      });
    } catch (error) {
      console.error('Error loading user data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    const result = await signOut();
    if (result.success) {
      navigate('/');
    }
  };

  const handleCancelBooking = async (bookingId) => {
    if (!confirm('Are you sure you want to cancel this booking?')) {
      return;
    }

    try {
      const result = await bookingService.cancelBooking(bookingId);
      if (result.error) {
        toast.error('Failed to cancel booking');
        return;
      }

      toast.success('Booking cancelled successfully');
      loadUserData(); // Refresh bookings
    } catch (error) {
      console.error('Error cancelling booking:', error);
      toast.error('Failed to cancel booking');
    }
  };

  const handleProfileUpdate = async (e) => {
    e.preventDefault();
    
    try {
      const result = await updateProfile({
        full_name: profileData.fullName,
        phone: profileData.phone
      });

      if (result.success) {
        toast.success('Profile updated successfully');
        setEditingProfile(false);
      } else {
        toast.error(result.error || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    }
  };

  const getBookingStatusColor = (status) => {
    switch (status) {
      case 'confirmed': return 'text-green-600 bg-green-100';
      case 'cancelled': return 'text-red-600 bg-red-100';
      case 'completed': return 'text-blue-600 bg-blue-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPaymentStatusColor = (status) => {
    switch (status) {
      case 'paid': return 'text-green-600 bg-green-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'failed': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="spinner mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-primary">My Dashboard</h1>
            <nav className="flex items-center space-x-4">
              <a href="/" className="text-gray-600 hover:text-primary">Home</a>
              <a href="/rooms" className="text-gray-600 hover:text-primary">Rooms</a>
              <button onClick={handleSignOut} className="btn btn-outline">
                Sign Out
              </button>
            </nav>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-6">
        {/* Welcome Section */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-2">
            Welcome back, {user?.user_metadata?.full_name || user?.email}!
          </h2>
          <p className="text-gray-600">
            Manage your bookings and profile information here.
          </p>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-md mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('bookings')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'bookings'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                My Bookings ({bookings.length})
              </button>
              <button
                onClick={() => setActiveTab('profile')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'profile'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Profile Settings
              </button>
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'bookings' && (
              <div>
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-semibold">My Bookings</h3>
                  <a href="/rooms" className="btn btn-primary">
                    Book New Room
                  </a>
                </div>

                {bookings.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">🏨</div>
                    <h4 className="text-xl font-semibold mb-2">No bookings yet</h4>
                    <p className="text-gray-600 mb-4">
                      Start planning your stay by browsing our available rooms.
                    </p>
                    <a href="/rooms" className="btn btn-primary">
                      Browse Rooms
                    </a>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {bookings.map((booking) => (
                      <div key={booking.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex flex-col md:flex-row md:items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <h4 className="font-semibold">
                                {booking.room?.room_type?.charAt(0).toUpperCase() + booking.room?.room_type?.slice(1)} Room
                              </h4>
                              <span className="text-sm text-gray-600">
                                Room {booking.room?.room_number}
                              </span>
                              <span className={`px-2 py-1 rounded text-xs font-medium ${getBookingStatusColor(booking.booking_status)}`}>
                                {booking.booking_status.charAt(0).toUpperCase() + booking.booking_status.slice(1)}
                              </span>
                            </div>
                            
                            <div className="text-sm text-gray-600 space-y-1">
                              <p>
                                <strong>Dates:</strong> {formatDateRange(booking.check_in_date, booking.check_out_date)}
                              </p>
                              <p>
                                <strong>Guests:</strong> {booking.guest_count}
                              </p>
                              <p>
                                <strong>Total:</strong> {formatCurrency(booking.total_amount)}
                              </p>
                              <p>
                                <strong>Payment:</strong> 
                                <span className={`ml-1 px-2 py-1 rounded text-xs font-medium ${getPaymentStatusColor(booking.payment_status)}`}>
                                  {booking.payment_status.charAt(0).toUpperCase() + booking.payment_status.slice(1)}
                                </span>
                              </p>
                              {booking.special_requests && (
                                <p>
                                  <strong>Special Requests:</strong> {booking.special_requests}
                                </p>
                              )}
                            </div>
                          </div>

                          <div className="mt-4 md:mt-0 md:ml-4 flex flex-col gap-2">
                            <span className="text-xs text-gray-500">
                              Booked on {formatDate(booking.created_at)}
                            </span>
                            
                            {booking.booking_status === 'confirmed' && (
                              <button
                                onClick={() => handleCancelBooking(booking.id)}
                                className="btn btn-outline text-red-600 border-red-600 hover:bg-red-600 hover:text-white text-sm"
                              >
                                Cancel Booking
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {activeTab === 'profile' && (
              <div>
                <h3 className="text-lg font-semibold mb-6">Profile Settings</h3>
                
                <form onSubmit={handleProfileUpdate} className="max-w-md space-y-4">
                  <div className="form-group">
                    <label className="form-label">Email Address</label>
                    <input
                      type="email"
                      value={user?.email || ''}
                      className="form-input bg-gray-100"
                      disabled
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Email cannot be changed. Contact support if needed.
                    </p>
                  </div>

                  <div className="form-group">
                    <label className="form-label">Full Name</label>
                    <input
                      type="text"
                      value={profileData.fullName}
                      onChange={(e) => setProfileData(prev => ({ ...prev, fullName: e.target.value }))}
                      className="form-input"
                      disabled={!editingProfile}
                      placeholder="Enter your full name"
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">Phone Number</label>
                    <input
                      type="tel"
                      value={profileData.phone}
                      onChange={(e) => setProfileData(prev => ({ ...prev, phone: e.target.value }))}
                      className="form-input"
                      disabled={!editingProfile}
                      placeholder="Enter your phone number"
                    />
                  </div>

                  <div className="flex gap-3">
                    {editingProfile ? (
                      <>
                        <button type="submit" className="btn btn-primary">
                          Save Changes
                        </button>
                        <button
                          type="button"
                          onClick={() => setEditingProfile(false)}
                          className="btn btn-outline"
                        >
                          Cancel
                        </button>
                      </>
                    ) : (
                      <button
                        type="button"
                        onClick={() => setEditingProfile(true)}
                        className="btn btn-primary"
                      >
                        Edit Profile
                      </button>
                    )}
                  </div>
                </form>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserDashboard;
