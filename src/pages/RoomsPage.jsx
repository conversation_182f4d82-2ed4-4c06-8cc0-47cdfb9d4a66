// Rooms listing page with search and filters

import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import RoomSearch from '../components/guest/RoomSearch';
import RoomCard from '../components/guest/RoomCard';
import { roomService } from '../services/roomService';
import toast from 'react-hot-toast';

const RoomsPage = () => {
  const location = useLocation();
  const [rooms, setRooms] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchFilters, setSearchFilters] = useState(null);
  const [sortBy, setSortBy] = useState('price_asc');

  // Load initial rooms on component mount
  useEffect(() => {
    loadInitialRooms();
  }, []);

  // Handle search params from URL (e.g., from homepage search)
  useEffect(() => {
    if (location.state?.searchParams) {
      // Auto-search with params from homepage
      handleSearchResults([], location.state.searchParams);
    }
  }, [location.state]);

  const loadInitialRooms = async () => {
    setLoading(true);
    try {
      const result = await roomService.getAllRooms();
      
      if (result.error) {
        toast.error('Failed to load rooms');
        return;
      }

      setRooms(result.data || []);
    } catch (error) {
      console.error('Error loading rooms:', error);
      toast.error('Failed to load rooms');
    } finally {
      setLoading(false);
    }
  };

  const handleSearchResults = (searchResults, filters) => {
    setRooms(searchResults);
    setSearchFilters(filters);
    
    // Apply current sorting
    const sortedResults = sortRooms(searchResults, sortBy);
    setRooms(sortedResults);
  };

  const handleFiltersChange = (filters) => {
    setSearchFilters(filters);
  };

  const sortRooms = (roomsToSort, sortOption) => {
    const sorted = [...roomsToSort];
    
    switch (sortOption) {
      case 'price_asc':
        return sorted.sort((a, b) => a.price_per_night - b.price_per_night);
      case 'price_desc':
        return sorted.sort((a, b) => b.price_per_night - a.price_per_night);
      case 'capacity_asc':
        return sorted.sort((a, b) => a.capacity - b.capacity);
      case 'capacity_desc':
        return sorted.sort((a, b) => b.capacity - a.capacity);
      case 'room_number':
        return sorted.sort((a, b) => a.room_number.localeCompare(b.room_number));
      case 'room_type':
        return sorted.sort((a, b) => a.room_type.localeCompare(b.room_type));
      default:
        return sorted;
    }
  };

  const handleSortChange = (newSortBy) => {
    setSortBy(newSortBy);
    const sortedRooms = sortRooms(rooms, newSortBy);
    setRooms(sortedRooms);
  };

  const getSortLabel = (sortOption) => {
    const labels = {
      price_asc: 'Price: Low to High',
      price_desc: 'Price: High to Low',
      capacity_asc: 'Capacity: Low to High',
      capacity_desc: 'Capacity: High to Low',
      room_number: 'Room Number',
      room_type: 'Room Type'
    };
    return labels[sortOption] || sortOption;
  };

  return (
    <div>
      {/* Header */}
      <header>
        <div>
          <div>
            <h1>
              🏨 Available Rooms
            </h1>
            <nav>
              <a href="/">
                ← Back to Home
              </a>
            </nav>
          </div>
        </div>
      </header>

      <div>
        {/* Search Component */}
        <RoomSearch
          onSearchResults={handleSearchResults}
          onFiltersChange={handleFiltersChange}
        />

        {/* Results Header */}
        <div>
          <div>
            <h2>
              {searchFilters ? 'Search Results' : 'All Available Rooms'}
            </h2>
            <p>
              {loading ? 'Loading...' : `${rooms.length} room${rooms.length !== 1 ? 's' : ''} found`}
            </p>
          </div>

          {/* Sort Options */}
          {!loading && rooms.length > 0 && (
            <div>
              <label>Sort by:</label>
              <select
                value={sortBy}
                onChange={(e) => handleSortChange(e.target.value)}
              >
                <option value="price_asc">Price: Low to High</option>
                <option value="price_desc">Price: High to Low</option>
                <option value="capacity_asc">Capacity: Low to High</option>
                <option value="capacity_desc">Capacity: High to Low</option>
                <option value="room_number">Room Number</option>
                <option value="room_type">Room Type</option>
              </select>
            </div>
          )}
        </div>

        {/* Loading State */}
        {loading && (
          <div>
            <div>
              <div className="spinner mx-auto mb-4"></div>
              <p>Loading rooms...</p>
            </div>
          </div>
        )}

        {/* No Results */}
        {!loading && rooms.length === 0 && (
          <div>
            <div>🏨</div>
            <h3>No rooms found</h3>
            <p>
              {searchFilters 
                ? 'Try adjusting your search filters to find available rooms.'
                : 'No rooms are currently available.'
              }
            </p>
            {searchFilters && (
              <button
                onClick={loadInitialRooms}
              >
                View All Rooms
              </button>
            )}
          </div>
        )}

        {/* Rooms Grid */}
        {!loading && rooms.length > 0 && (
          <div>
            {rooms.map((room) => (
              <RoomCard
                key={room.id}
                room={room}
                searchFilters={searchFilters}
                showBookButton={true}
              />
            ))}
          </div>
        )}

        {/* Results Summary */}
        {!loading && rooms.length > 0 && searchFilters && (
          <div>
            <h4>Search Summary</h4>
            <div>
              {searchFilters.checkIn && searchFilters.checkOut && (
                <p>
                  <strong>Dates:</strong> {searchFilters.checkIn} to {searchFilters.checkOut}
                </p>
              )}
              {searchFilters.guests > 1 && (
                <p>
                  <strong>Guests:</strong> {searchFilters.guests}
                </p>
              )}
              {searchFilters.roomType !== 'all' && (
                <p>
                  <strong>Room Type:</strong> {searchFilters.roomType.charAt(0).toUpperCase() + searchFilters.roomType.slice(1)}
                </p>
              )}
              {(searchFilters.minPrice || searchFilters.maxPrice) && (
                <p>
                  <strong>Price Range:</strong> 
                  {searchFilters.minPrice && ` From ₵${searchFilters.minPrice}`}
                  {searchFilters.maxPrice && ` To ₵${searchFilters.maxPrice}`}
                </p>
              )}
              {searchFilters.amenities && searchFilters.amenities.length > 0 && (
                <p>
                  <strong>Amenities:</strong> {searchFilters.amenities.join(', ')}
                </p>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RoomsPage;
