// Booking page for creating reservations
import { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { bookingService } from '../services/bookingService';
import { roomService } from '../services/roomService';
import { formatCurrency } from '../utils/currency';
import { calculateNights, formatDateRange, validateDateRange } from '../utils/date';
import toast from 'react-hot-toast';

const BookingPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();

  const [room, setRoom] = useState(location.state?.room || null);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const [bookingData, setBookingData] = useState({
    checkIn: location.state?.bookingData?.checkIn || location.state?.searchFilters?.checkIn || '',
    checkOut: location.state?.bookingData?.checkOut || location.state?.searchFilters?.checkOut || '',
    guests: location.state?.bookingData?.guests || location.state?.searchFilters?.guests || 1,
    specialRequests: ''
  });

  const [guestInfo, setGuestInfo] = useState({
    fullName: '',
    email: '',
    phone: ''
  });

  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (!isAuthenticated()) {
      navigate('/login', { 
        state: { 
          returnTo: '/booking',
          room,
          bookingData 
        } 
      });
      return;
    }

    // Load room if not provided
    if (!room && location.state?.roomId) {
      loadRoom(location.state.roomId);
    }

    // Pre-fill guest info from user profile
    if (user) {
      setGuestInfo({
        fullName: user.user_metadata?.full_name || '',
        email: user.email || '',
        phone: user.user_metadata?.phone || ''
      });
    }
  }, [user, room, isAuthenticated]);

  const loadRoom = async (roomId) => {
    setLoading(true);
    try {
      const result = await roomService.getRoomById(roomId);
      if (result.error) {
        toast.error('Room not found');
        navigate('/rooms');
        return;
      }
      setRoom(result.data);
    } catch (error) {
      console.error('Error loading room:', error);
      toast.error('Failed to load room details');
      navigate('/rooms');
    } finally {
      setLoading(false);
    }
  };

  const handleBookingDataChange = (key, value) => {
    setBookingData(prev => ({ ...prev, [key]: value }));
    if (errors[key]) {
      setErrors(prev => ({ ...prev, [key]: '' }));
    }
  };

  const handleGuestInfoChange = (key, value) => {
    setGuestInfo(prev => ({ ...prev, [key]: value }));
    if (errors[key]) {
      setErrors(prev => ({ ...prev, [key]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Date validation
    if (!bookingData.checkIn) {
      newErrors.checkIn = 'Check-in date is required';
    }

    if (!bookingData.checkOut) {
      newErrors.checkOut = 'Check-out date is required';
    }

    if (bookingData.checkIn && bookingData.checkOut) {
      const validation = validateDateRange(bookingData.checkIn, bookingData.checkOut);
      if (!validation.isValid) {
        newErrors.dateRange = validation.errors[0];
      }
    }

    // Guest count validation
    if (bookingData.guests > room?.capacity) {
      newErrors.guests = `Maximum ${room.capacity} guests allowed for this room`;
    }

    // Guest info validation
    if (!guestInfo.fullName.trim()) {
      newErrors.fullName = 'Full name is required';
    }

    if (!guestInfo.email.trim()) {
      newErrors.email = 'Email is required';
    }

    if (!guestInfo.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const calculateTotal = () => {
    if (!bookingData.checkIn || !bookingData.checkOut || !room) return null;

    const nights = calculateNights(bookingData.checkIn, bookingData.checkOut);
    const subtotal = room.price_per_night * nights;
    const total = subtotal; // No additional fees for now

    return {
      nights,
      pricePerNight: room.price_per_night,
      subtotal,
      total,
      dateRange: formatDateRange(bookingData.checkIn, bookingData.checkOut)
    };
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fix the errors below');
      return;
    }

    // Check availability one more time
    const availability = await roomService.checkAvailability(
      room.id,
      bookingData.checkIn,
      bookingData.checkOut
    );

    if (!availability.available) {
      toast.error('Room is no longer available for selected dates');
      return;
    }

    setSubmitting(true);

    try {
      const pricing = calculateTotal();
      
      const booking = {
        guestId: user.id,
        roomId: room.id,
        checkIn: bookingData.checkIn,
        checkOut: bookingData.checkOut,
        guestCount: bookingData.guests,
        totalAmount: pricing.total,
        specialRequests: bookingData.specialRequests
      };

      const result = await bookingService.createBooking(booking);

      if (result.error) {
        toast.error('Failed to create booking. Please try again.');
        return;
      }

      toast.success('Booking created successfully!');
      navigate('/dashboard', { 
        state: { 
          newBooking: result.data,
          showBookingSuccess: true 
        } 
      });

    } catch (error) {
      console.error('Booking error:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setSubmitting(false);
    }
  };

  const pricing = calculateTotal();

  if (loading) {
    return (
      <div>
        <div>
          <div className="spinner mx-auto mb-4"></div>
          <p>Loading booking details...</p>
        </div>
      </div>
    );
  }

  if (!room) {
    return (
      <div>
        <div>
          <h2>Room Not Found</h2>
          <button onClick={() => navigate('/rooms')}>
            Back to Rooms
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <header>
        <div>
          <div>
            <button
              onClick={() => navigate(-1)}
            >
              ← Back
            </button>
            <h1>Complete Your Booking</h1>
            <div></div>
          </div>
        </div>
      </header>

      <div>
        <div>
          {/* Booking Form */}
          <div>
            <form onSubmit={handleSubmit}>
              {/* Booking Details */}
              <div>
                <h3>Booking Details</h3>
                <div>
                  <div className="form-group">
                    <label className="form-label">Check-in Date</label>
                    <input
                      type="date"
                      value={bookingData.checkIn}
                      onChange={(e) => handleBookingDataChange('checkIn', e.target.value)}
                      className={`form-input ${errors.checkIn ? 'border-red-500' : ''}`}
                      min={new Date().toISOString().split('T')[0]}
                    />
                    {errors.checkIn && <p className="form-error">{errors.checkIn}</p>}
                  </div>

                  <div className="form-group">
                    <label className="form-label">Check-out Date</label>
                    <input
                      type="date"
                      value={bookingData.checkOut}
                      onChange={(e) => handleBookingDataChange('checkOut', e.target.value)}
                      className={`form-input ${errors.checkOut ? 'border-red-500' : ''}`}
                      min={bookingData.checkIn || new Date().toISOString().split('T')[0]}
                    />
                    {errors.checkOut && <p className="form-error">{errors.checkOut}</p>}
                  </div>
                </div>

                {errors.dateRange && (
                  <p className="form-error mb-4">{errors.dateRange}</p>
                )}

                <div className="form-group">
                  <label className="form-label">Number of Guests</label>
                  <select
                    value={bookingData.guests}
                    onChange={(e) => handleBookingDataChange('guests', parseInt(e.target.value))}
                    className={`form-input ${errors.guests ? 'border-red-500' : ''}`}
                  >
                    {Array.from({ length: room.capacity }, (_, i) => i + 1).map(num => (
                      <option key={num} value={num}>
                        {num} Guest{num > 1 ? 's' : ''}
                      </option>
                    ))}
                  </select>
                  {errors.guests && <p className="form-error">{errors.guests}</p>}
                </div>

                <div className="form-group">
                  <label className="form-label">Special Requests (Optional)</label>
                  <textarea
                    value={bookingData.specialRequests}
                    onChange={(e) => handleBookingDataChange('specialRequests', e.target.value)}
                    className="form-input"
                    rows="3"
                    placeholder="Any special requests or requirements..."
                  />
                </div>
              </div>

              {/* Guest Information */}
              <div>
                <h3>Guest Information</h3>
                
                <div>
                  <div className="form-group">
                    <label className="form-label">Full Name</label>
                    <input
                      type="text"
                      value={guestInfo.fullName}
                      onChange={(e) => handleGuestInfoChange('fullName', e.target.value)}
                      className={`form-input ${errors.fullName ? 'border-red-500' : ''}`}
                      placeholder="Enter full name"
                    />
                    {errors.fullName && <p className="form-error">{errors.fullName}</p>}
                  </div>

                  <div className="form-group">
                    <label className="form-label">Email Address</label>
                    <input
                      type="email"
                      value={guestInfo.email}
                      onChange={(e) => handleGuestInfoChange('email', e.target.value)}
                      className={`form-input ${errors.email ? 'border-red-500' : ''}`}
                      placeholder="Enter email address"
                    />
                    {errors.email && <p className="form-error">{errors.email}</p>}
                  </div>

                  <div className="form-group">
                    <label className="form-label">Phone Number</label>
                    <input
                      type="tel"
                      value={guestInfo.phone}
                      onChange={(e) => handleGuestInfoChange('phone', e.target.value)}
                      className={`form-input ${errors.phone ? 'border-red-500' : ''}`}
                      placeholder="Enter phone number"
                    />
                    {errors.phone && <p className="form-error">{errors.phone}</p>}
                  </div>
                </div>
              </div>

              {/* Submit Button */}
              <div>
                <button
                  type="submit"
                  disabled={submitting || !pricing}
                >
                  {submitting ? (
                    <div>
                      <div className="spinner mr-2"></div>
                      Creating Booking...
                    </div>
                  ) : (
                    `Confirm Booking - ${pricing ? formatCurrency(pricing.total) : ''}`
                  )}
                </button>
                
                <p>
                  By confirming, you agree to our terms and conditions
                </p>
              </div>
            </form>
          </div>

          {/* Booking Summary */}
          <div>
            <div>
              <h3>Booking Summary</h3>

              {/* Room Info */}
              <div>
                <h4>{room.room_type.charAt(0).toUpperCase() + room.room_type.slice(1)} Room</h4>
                <p>Room {room.room_number}</p>
                <p>Up to {room.capacity} guests</p>
              </div>

              {/* Pricing Breakdown */}
              {pricing && (
                <div>
                  <div>
                    <span>{formatCurrency(pricing.pricePerNight)} × {pricing.nights} night{pricing.nights > 1 ? 's' : ''}</span>
                    <span>{formatCurrency(pricing.subtotal)}</span>
                  </div>
                  
                  <hr />
                  
                  <div>
                    <span>Total</span>
                    <span>{formatCurrency(pricing.total)}</span>
                  </div>
                  
                  <div>
                    {pricing.dateRange}
                  </div>
                </div>
              )}

              {/* Payment Info */}
              <div>
                <h5>Payment Information</h5>
                <p>
                  Payment will be processed upon arrival. We accept cash, mobile money, and cards.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingPage;
