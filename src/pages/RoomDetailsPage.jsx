// Room details page with booking option

import { useState, useEffect } from 'react';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import { roomService } from '../services/roomService';
import { formatCurrency } from '../utils/currency';
import { calculateNights, formatDateRange, validateDateRange, getMinCheckInDate } from '../utils/date';
import { useAuth } from '../context/AuthContext';
import ReviewDisplay from '../components/guest/ReviewDisplay';
import toast from 'react-hot-toast';

const RoomDetailsPage = () => {
  const { id } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();

  const [room, setRoom] = useState(null);
  const [loading, setLoading] = useState(true);
  const [checkingAvailability, setCheckingAvailability] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  
  // Booking form state
  const [bookingData, setBookingData] = useState({
    checkIn: location.state?.searchFilters?.checkIn || '',
    checkOut: location.state?.searchFilters?.checkOut || '',
    guests: location.state?.searchFilters?.guests || 1
  });

  const [availability, setAvailability] = useState(null);

  useEffect(() => {
    loadRoomDetails();
  }, [id]);

  useEffect(() => {
    // Check availability when dates change
    if (bookingData.checkIn && bookingData.checkOut && room) {
      checkAvailability();
    }
  }, [bookingData.checkIn, bookingData.checkOut, room]);

  const loadRoomDetails = async () => {
    setLoading(true);
    try {
      const result = await roomService.getRoomById(id);
      
      if (result.error) {
        toast.error('Room not found');
        navigate('/rooms');
        return;
      }

      setRoom(result.data);
    } catch (error) {
      console.error('Error loading room details:', error);
      toast.error('Failed to load room details');
      navigate('/rooms');
    } finally {
      setLoading(false);
    }
  };

  const checkAvailability = async () => {
    if (!room || !bookingData.checkIn || !bookingData.checkOut) return;

    const validation = validateDateRange(bookingData.checkIn, bookingData.checkOut);
    if (!validation.isValid) return;

    setCheckingAvailability(true);
    try {
      const result = await roomService.checkAvailability(
        room.id,
        bookingData.checkIn,
        bookingData.checkOut
      );

      setAvailability(result);
    } catch (error) {
      console.error('Error checking availability:', error);
    } finally {
      setCheckingAvailability(false);
    }
  };

  const handleBookingDataChange = (key, value) => {
    setBookingData(prev => ({ ...prev, [key]: value }));
  };

  const handleBookNow = () => {
    if (!isAuthenticated()) {
      navigate('/login', {
        state: {
          returnTo: `/rooms/${room.id}`,
          searchFilters: bookingData
        }
      });
      return;
    }

    // Validate booking data
    if (!bookingData.checkIn || !bookingData.checkOut) {
      toast.error('Please select check-in and check-out dates');
      return;
    }

    const validation = validateDateRange(bookingData.checkIn, bookingData.checkOut);
    if (!validation.isValid) {
      toast.error(validation.errors[0]);
      return;
    }

    if (bookingData.guests > room.capacity) {
      toast.error(`This room can accommodate maximum ${room.capacity} guests`);
      return;
    }

    if (!availability?.available) {
      toast.error('Room is not available for selected dates');
      return;
    }

    // Navigate to booking page
    navigate('/booking', {
      state: {
        room,
        bookingData
      }
    });
  };

  const calculateTotal = () => {
    if (!bookingData.checkIn || !bookingData.checkOut || !room) return null;

    const nights = calculateNights(bookingData.checkIn, bookingData.checkOut);
    const total = room.price_per_night * nights;

    return {
      nights,
      pricePerNight: room.price_per_night,
      total,
      dateRange: formatDateRange(bookingData.checkIn, bookingData.checkOut)
    };
  };

  const pricing = calculateTotal();

  if (loading) {
    return (
      <div>
        <div>
          <div className="spinner mx-auto mb-4"></div>
          <p>Loading room details...</p>
        </div>
      </div>
    );
  }

  if (!room) {
    return (
      <div>
        <div>
          <h2>Room Not Found</h2>
          <button onClick={() => navigate('/rooms')}>
            Back to Rooms
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <header>
        <div>
          <div>
            <button
              onClick={() => navigate('/rooms')}
            >
              ← Back to Rooms
            </button>
            <h1>Room Details</h1>
            <div></div>
          </div>
        </div>
      </header>

      <div>
        <div>
          {/* Room Details */}
          <div>
            {/* Image Gallery */}
            <div>
              <div>
                {room.images && room.images.length > 0 ? (
                  <img
                    src={room.images[selectedImageIndex]}
                    alt={`${room.room_type} room ${room.room_number}`}
                  />
                ) : (
                  <div>
                    <div>
                      <div>🏨</div>
                      <span>Room Image</span>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Image Thumbnails */}
              {room.images && room.images.length > 1 && (
                <div>
                  {room.images.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setSelectedImageIndex(index)}
                      className={`flex-shrink-0 w-16 h-16 rounded border-2 overflow-hidden ${
                        selectedImageIndex === index ? 'border-primary' : 'border-gray-300'
                      }`}
                    >
                      <img
                        src={image}
                        alt={`Room view ${index + 1}`}
                      />
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Room Information */}
            <div>
              <div>
                <h2>
                  {room.room_type.charAt(0).toUpperCase() + room.room_type.slice(1)} Room
                </h2>
                <span>
                  {room.room_number}
                </span>
              </div>

              <div>
                <div>
                  <span>Capacity:</span>
                  <span>
                    Up to {room.capacity} guest{room.capacity > 1 ? 's' : ''}
                  </span>
                </div>
                <div>
                  <span>Price:</span>
                  <span>
                    {formatCurrency(room.price_per_night)}/night
                  </span>
                </div>
              </div>

              {room.description && (
                <div>
                  <h3>Description</h3>
                  <p>{room.description}</p>
                </div>
              )}

              {/* Amenities */}
              {room.amenities && room.amenities.length > 0 && (
                <div>
                  <h3>Amenities</h3>
                  <div>
                    {room.amenities.map((amenity) => (
                      <div key={amenity}>
                        <span>✓</span>
                        <span>{amenity}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Booking Card */}
          <div>
            <div>
              <h3>Book This Room</h3>

              {/* Date Selection */}
              <div>
                <div>
                  <label className="form-label">Check-in Date</label>
                  <input
                    type="date"
                    value={bookingData.checkIn}
                    onChange={(e) => handleBookingDataChange('checkIn', e.target.value)}
                    className="form-input"
                    min={new Date().toISOString().split('T')[0]}
                  />
                </div>

                <div>
                  <label className="form-label">Check-out Date</label>
                  <input
                    type="date"
                    value={bookingData.checkOut}
                    onChange={(e) => handleBookingDataChange('checkOut', e.target.value)}
                    className="form-input"
                    min={bookingData.checkIn || new Date().toISOString().split('T')[0]}
                  />
                </div>

                <div>
                  <label className="form-label">Guests</label>
                  <select
                    value={bookingData.guests}
                    onChange={(e) => handleBookingDataChange('guests', parseInt(e.target.value))}
                    className="form-input"
                  >
                    {Array.from({ length: room.capacity }, (_, i) => i + 1).map(num => (
                      <option key={num} value={num}>
                        {num} Guest{num > 1 ? 's' : ''}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Availability Status */}
              {checkingAvailability && (
                <div>
                  <div className="spinner mx-auto mb-2"></div>
                  <span>Checking availability...</span>
                </div>
              )}

              {availability && bookingData.checkIn && bookingData.checkOut && !checkingAvailability && (
                <div className={`mb-4 p-3 rounded ${
                  availability.available 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {availability.available ? '✓ Available' : '✗ Not Available'}
                </div>
              )}

              {/* Pricing Summary */}
              {pricing && (
                <div>
                  <div>
                    <div>
                      <span>{formatCurrency(pricing.pricePerNight)} × {pricing.nights} night{pricing.nights > 1 ? 's' : ''}</span>
                      <span>{formatCurrency(pricing.total)}</span>
                    </div>
                  </div>
                  <hr className="my-2" />
                  <div>
                    <span>Total</span>
                    <span>{formatCurrency(pricing.total)}</span>
                  </div>
                  <div>
                    {pricing.dateRange}
                  </div>
                </div>
              )}

              {/* Book Button */}
              <button
                onClick={handleBookNow}
                disabled={
                  !bookingData.checkIn || 
                  !bookingData.checkOut || 
                  checkingAvailability ||
                  (availability && !availability.available)
                }
              >
                {!isAuthenticated() 
                  ? 'Sign In to Book' 
                  : availability?.available === false
                  ? 'Not Available'
                  : 'Book Now'
                }
              </button>

              {!isAuthenticated() && (
                <p>
                  You'll be redirected to sign in
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Reviews Section */}
        <div>
          <ReviewDisplay roomId={room.id} />
        </div>
      </div>
    </div>
  );
};

export default RoomDetailsPage;
